﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WHO.MALARIA.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddColumn_ReportCompletenessMetNotMetField_In_DeskLevelSummary : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<bool>(
                name: "IsDeactivated",
                schema: "Internal",
                table: "Identity",
                type: "BIT",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "bit");

            migrationBuilder.AddColumn<bool>(
                name: "ReportCompletenessMetNotMet",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "bit",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ReportCompletenessMetNotMet",
                schema: "DQA",
                table: "DeskLevelSummary");

            migrationBuilder.AlterColumn<bool>(
                name: "IsDeactivated",
                schema: "Internal",
                table: "Identity",
                type: "bit",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "BIT",
                oldDefaultValue: false);
        }
    }
}
