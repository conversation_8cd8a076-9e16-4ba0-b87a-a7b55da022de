﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WHO.MALARIA.Database;

#nullable disable

namespace WHO.MALARIA.Database.Migrations
{
    [DbContext(typeof(MalariaDbContext))]
    [Migration("20250917142515_AddColumn_ReportCompletenessMetNotMetField_In_DeskLevelSummary")]
    partial class AddColumn_ReportCompletenessMetNotMetField_In_DeskLevelSummary
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AnalyticalOutputIndicatorStrategyMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IndicatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("IndicatorId");

                    b.Property<Guid>("StrategyId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("StrategyId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("IndicatorId");

                    b.HasIndex("StrategyId");

                    b.HasIndex("IndicatorId", "StrategyId")
                        .IsUnique();

                    b.ToTable("AnalyticalOutputIndicatorStrategyMapping", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Assessment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<int>("Approach")
                        .HasColumnType("INT")
                        .HasColumnName("Approach");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("CountryId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("DATETIME")
                        .HasColumnName("EndDate");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("IsActive");

                    b.Property<bool>("ShowResultsOnGlobalDashboard")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(false)
                        .HasColumnName("ShowResultsOnGlobalDashboard");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("DATETIME")
                        .HasColumnName("StartDate");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.ToTable("Assessment", "ScopeDefinition");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentDRResponse", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentIndicatorId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentIndicatorId");

                    b.Property<Guid>("AssessmentStrategyId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentStrategyId");

                    b.Property<bool>("CannotBeAssessed")
                        .HasColumnType("BIT")
                        .HasColumnName("CannotBeAssessed");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte?>("MetNotMetStatus")
                        .HasColumnType("TINYINT")
                        .HasColumnName("MetNotMetStatus");

                    b.Property<string>("ResponseJson")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(MAX)")
                        .HasColumnName("ResponseJson");

                    b.Property<int>("Status")
                        .HasColumnType("INT")
                        .HasColumnName("Status");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentStrategyId");

                    b.HasIndex("AssessmentIndicatorId", "AssessmentStrategyId")
                        .IsUnique();

                    b.ToTable("AssessmentDRResponse", "DeskReview");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentIndicator", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IndicatorId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("IndicatorId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.HasIndex("IndicatorId");

                    b.HasIndex("AssessmentId", "IndicatorId")
                        .IsUnique();

                    b.ToTable("AssessmentIndicator", "ScopeDefinition");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentStatus", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("INT")
                        .HasColumnName("Status");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("AssessmentStatus", "ScopeDefinition");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentStrategy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("StrategyId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("StrategyId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.HasIndex("StrategyId");

                    b.HasIndex("AssessmentId", "StrategyId")
                        .IsUnique();

                    b.ToTable("AssessmentStrategy", "ScopeDefinition");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .HasColumnType("BIT")
                        .HasColumnName("IsActive");

                    b.Property<int>("Role")
                        .HasColumnType("INT")
                        .HasColumnName("Role");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("UserId");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.HasIndex("UserId");

                    b.ToTable("AssessmentUser", "ScopeDefinition");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AuditLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<string>("ChangedColumns")
                        .HasColumnType("NVARCHAR(MAX)")
                        .HasColumnName("ChangedColumns");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateTimeUtc")
                        .HasColumnType("DATETIME")
                        .HasColumnName("DateTimeUtc");

                    b.Property<string>("KeyValues")
                        .HasColumnType("NVARCHAR(500)")
                        .HasColumnName("KeyValues");

                    b.Property<string>("NewValues")
                        .HasColumnType("NVARCHAR(MAX)")
                        .HasColumnName("NewValues");

                    b.Property<string>("OldValues")
                        .HasColumnType("NVARCHAR(MAX)")
                        .HasColumnName("OldValues");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("TableName");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("VARCHAR(10)")
                        .HasColumnName("Type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("UserId");

                    b.HasKey("Id");

                    b.ToTable("AuditLog", (string)null);
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Country", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ISO")
                        .IsRequired()
                        .HasColumnType("CHAR(3)")
                        .HasColumnName("ISO");

                    b.Property<bool>("IsActive")
                        .HasColumnType("BIT")
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Name");

                    b.Property<string>("Name_FR")
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Name_FR");

                    b.Property<Guid>("RegionId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("RegionId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("RegionId");

                    b.ToTable("Country", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DQAConsistencyVariable", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("Name");

                    b.Property<string>("Name_FR")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("Name_FR");

                    b.Property<short>("Order")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Order");

                    b.Property<byte>("Priority")
                        .HasColumnType("TINYINT")
                        .HasColumnName("Priority");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("DQAConsistencyVariable", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DQAIndicator", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IndicatorId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("IndicatorId");

                    b.Property<int>("Type")
                        .HasColumnType("INT")
                        .HasColumnName("Type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("IndicatorId");

                    b.HasIndex("IndicatorId", "Type")
                        .IsUnique();

                    b.ToTable("DQAIndicator", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DQAVariableStrategyMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DQAVariableId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DQAVariableId");

                    b.Property<Guid>("StrategyId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("StrategyId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("DQAVariableId");

                    b.HasIndex("StrategyId");

                    b.ToTable("DQAVariableStrategyMapping", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.DataSource", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DataSourceId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("DataSourceId");

                    b.Property<Guid>("DeskLevelId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("DeskLevelId");

                    b.Property<string>("OtherSystemName")
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("OtherSystemName");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("DataSourceId");

                    b.HasIndex("DeskLevelId");

                    b.ToTable("DeskLevelDataSource", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.DeskLevel", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("FileName");

                    b.Property<bool>("IsFinalized")
                        .HasColumnType("BIT")
                        .HasColumnName("IsFinalized");

                    b.Property<short?>("TemplateVersion")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("TemplateVersion");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("DeskLevel", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.DeskLevelDataSystem1", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INT")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ANC")
                        .HasColumnType("INT")
                        .HasColumnName("ANC");

                    b.Property<int?>("AllCauseDeaths")
                        .HasColumnType("INT")
                        .HasColumnName("AllCauseDeaths");

                    b.Property<int?>("AllCauseInpatients")
                        .HasColumnType("INT")
                        .HasColumnName("AllCauseInpatients");

                    b.Property<int?>("AllCauseOutpatients")
                        .HasColumnType("INT")
                        .HasColumnName("AllCauseOutpatients");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<int?>("ConfirmMalariaCases")
                        .HasColumnType("INT")
                        .HasColumnName("ConfirmMalariaCases");

                    b.Property<int?>("ConfirmedMalariaCasesTreated")
                        .HasColumnType("INT")
                        .HasColumnName("ConfirmedMalariaCasesTreated");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("District")
                        .HasColumnType("NVARCHAR(150)")
                        .HasColumnName("District");

                    b.Property<string>("HealthFacilityName")
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("HealthFacilityName");

                    b.Property<string>("HealthFacilityType")
                        .HasColumnType("NVARCHAR(20)")
                        .HasColumnName("HealthFacilityType");

                    b.Property<int?>("IPTp")
                        .HasColumnType("INT")
                        .HasColumnName("IPTp");

                    b.Property<bool>("IsExpectedReports")
                        .HasColumnType("BIT")
                        .HasColumnName("IsExpected");

                    b.Property<bool>("IsReportsOnTime")
                        .HasColumnType("BIT")
                        .HasColumnName("IsOnTime");

                    b.Property<bool>("IsReportsReceived")
                        .HasColumnType("BIT")
                        .HasColumnName("IsReceived");

                    b.Property<int?>("MalariaInpatientDeaths")
                        .HasColumnType("INT")
                        .HasColumnName("MalariaInpatientDeaths");

                    b.Property<int?>("MalariaInpatients")
                        .HasColumnType("INT")
                        .HasColumnName("MalariaInpatients");

                    b.Property<int?>("MicroscopyPositive")
                        .HasColumnType("INT")
                        .HasColumnName("MicroscopyPositive");

                    b.Property<int?>("MicroscopyTested")
                        .HasColumnType("INT")
                        .HasColumnName("MicroscopyTested");

                    b.Property<byte>("Month")
                        .HasColumnType("TINYINT")
                        .HasColumnName("Month");

                    b.Property<int?>("PresumedMalariaCases")
                        .HasColumnType("INT")
                        .HasColumnName("PresumedMalariaCases");

                    b.Property<string>("Province")
                        .HasColumnType("NVARCHAR(150)")
                        .HasColumnName("Province");

                    b.Property<int?>("RDTPositive")
                        .HasColumnType("INT")
                        .HasColumnName("RDTPositive");

                    b.Property<int?>("RDTTested")
                        .HasColumnType("INT")
                        .HasColumnName("RDTTested");

                    b.Property<int?>("SuspectedMalariaCases")
                        .HasColumnType("INT")
                        .HasColumnName("SuspectedMalariaCases");

                    b.Property<int?>("TotalMalariaCases")
                        .HasColumnType("INT")
                        .HasColumnName("TotalMalariaCases");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<short>("Year")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("DeskLevelDataSystem1", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.DeskLevelDataSystem2", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INT")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ANC")
                        .HasColumnType("INT")
                        .HasColumnName("ANC");

                    b.Property<int?>("AllCauseDeaths")
                        .HasColumnType("INT")
                        .HasColumnName("AllCauseDeaths");

                    b.Property<int?>("AllCauseInpatients")
                        .HasColumnType("INT")
                        .HasColumnName("AllCauseInpatients");

                    b.Property<int?>("AllCauseOutpatients")
                        .HasColumnType("INT")
                        .HasColumnName("AllCauseOutpatients");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<int?>("ConfirmMalariaCases")
                        .HasColumnType("INT")
                        .HasColumnName("ConfirmMalariaCases");

                    b.Property<int?>("ConfirmedMalariaCasesTreated")
                        .HasColumnType("INT")
                        .HasColumnName("ConfirmedMalariaCasesTreated");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("District")
                        .HasColumnType("NVARCHAR(150)")
                        .HasColumnName("District");

                    b.Property<string>("HealthFacilityName")
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("HealthFacilityName");

                    b.Property<string>("HealthFacilityType")
                        .HasColumnType("NVARCHAR(20)")
                        .HasColumnName("HealthFacilityType");

                    b.Property<int?>("IPTp")
                        .HasColumnType("INT")
                        .HasColumnName("IPTp");

                    b.Property<bool>("IsExpectedReports")
                        .HasColumnType("BIT")
                        .HasColumnName("IsExpected");

                    b.Property<bool>("IsReportsOnTime")
                        .HasColumnType("BIT")
                        .HasColumnName("IsOnTime");

                    b.Property<bool>("IsReportsReceived")
                        .HasColumnType("BIT")
                        .HasColumnName("IsReceived");

                    b.Property<int?>("MalariaInpatientDeaths")
                        .HasColumnType("INT")
                        .HasColumnName("MalariaInpatientDeaths");

                    b.Property<int?>("MalariaInpatients")
                        .HasColumnType("INT")
                        .HasColumnName("MalariaInpatients");

                    b.Property<int?>("MicroscopyPositive")
                        .HasColumnType("INT")
                        .HasColumnName("MicroscopyPositive");

                    b.Property<int?>("MicroscopyTested")
                        .HasColumnType("INT")
                        .HasColumnName("MicroscopyTested");

                    b.Property<byte>("Month")
                        .HasColumnType("TINYINT")
                        .HasColumnName("Month");

                    b.Property<int?>("PresumedMalariaCases")
                        .HasColumnType("INT")
                        .HasColumnName("PresumedMalariaCases");

                    b.Property<string>("Province")
                        .HasColumnType("NVARCHAR(150)")
                        .HasColumnName("Province");

                    b.Property<int?>("RDTPositive")
                        .HasColumnType("INT")
                        .HasColumnName("RDTPositive");

                    b.Property<int?>("RDTTested")
                        .HasColumnType("INT")
                        .HasColumnName("RDTTested");

                    b.Property<int?>("SuspectedMalariaCases")
                        .HasColumnType("INT")
                        .HasColumnName("SuspectedMalariaCases");

                    b.Property<int?>("TotalMalariaCases")
                        .HasColumnType("INT")
                        .HasColumnName("TotalMalariaCases");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<short>("Year")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("DeskLevelDataSystem2", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.Summary", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("DataSoucesError")
                        .HasColumnType("FLOAT")
                        .HasColumnName("DataSoucesError");

                    b.Property<bool>("IsFinalized")
                        .HasColumnType("BIT")
                        .HasColumnName("IsFinalized");

                    b.Property<double?>("KeyVariableCompletenessWithinRegister")
                        .HasColumnType("FLOAT")
                        .HasColumnName("KeyVariableCompletenessWithinRegister");

                    b.Property<double?>("KeyVariableConcordanceBtwRegister")
                        .HasColumnType("FLOAT")
                        .HasColumnName("KeyVariableConcordanceBtwRegister");

                    b.Property<bool?>("MalariaInPatientDeathProportion")
                        .HasColumnType("BIT")
                        .HasColumnName("MalariaInPatientDeathProportion");

                    b.Property<bool?>("MalariaInPatientProportion")
                        .HasColumnType("BIT")
                        .HasColumnName("MalariaInPatientProportion");

                    b.Property<bool?>("MalariaOutpatientProportion")
                        .HasColumnType("BIT")
                        .HasColumnName("MalariaOutpatientProportion");

                    b.Property<bool?>("RDTPositivityRate")
                        .HasColumnType("BIT")
                        .HasColumnName("RDTPositivityRate");

                    b.Property<double?>("ReportCompleteness")
                        .HasColumnType("FLOAT")
                        .HasColumnName("ReportCompleteness");

                    b.Property<bool?>("ReportCompletenessMetNotMet")
                        .HasColumnType("bit");

                    b.Property<double?>("ReportTimeliness")
                        .HasColumnType("FLOAT")
                        .HasColumnName("ReportTimeliness");

                    b.Property<bool?>("SlidePositivityRate")
                        .HasColumnType("BIT")
                        .HasColumnName("SlidePositivityRate");

                    b.Property<bool?>("SuspectedTestProportion")
                        .HasColumnType("BIT")
                        .HasColumnName("SuspectedTestProportion");

                    b.Property<bool?>("TestPositivityRate")
                        .HasColumnType("BIT")
                        .HasColumnName("TestPositivityRate");

                    b.Property<byte>("Type")
                        .HasColumnType("TINYINT")
                        .HasColumnName("Type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("VariableCompleteness")
                        .HasColumnType("FLOAT")
                        .HasColumnName("VariableCompleteness");

                    b.Property<double?>("VariableConcordance")
                        .HasColumnType("FLOAT")
                        .HasColumnName("VariableConcordance");

                    b.Property<double?>("VariableConsistency")
                        .HasColumnType("FLOAT")
                        .HasColumnName("VariableConsistency");

                    b.Property<short>("Year")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("DeskLevelSummary", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.SummaryDataQualityResultReason", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsFinalized")
                        .HasColumnType("BIT")
                        .HasColumnName("IsFinalized");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("Reason");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<short>("Year")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("DeskLevelSummaryDataQualityResultReason", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.VariableMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool?>("Concordance")
                        .HasColumnType("BIT")
                        .HasColumnName("Concordance");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DeskLevelId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("DeskLevelId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("VariableId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("VariableId");

                    b.HasKey("Id");

                    b.HasIndex("DeskLevelId");

                    b.HasIndex("VariableId");

                    b.ToTable("DeskLevelVariable", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.Elimination.Summary", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<double?>("CaseInvestigationReportsCompleteness")
                        .HasColumnType("FLOAT")
                        .HasColumnName("CaseInvestigationReportsCompleteness");

                    b.Property<double?>("CaseInvestigationReportsTimeliness")
                        .HasColumnType("FLOAT")
                        .HasColumnName("CaseInvestigationReportsTimeliness");

                    b.Property<double?>("CaseNotificationReportsTimeliness")
                        .HasColumnType("FLOAT")
                        .HasColumnName("CaseNotificationReportsTimeliness");

                    b.Property<bool?>("ConfirmMalariaCasesClassified")
                        .HasColumnType("BIT")
                        .HasColumnName("ConfirmMalariaCasesClassified");

                    b.Property<bool?>("ConfirmMalariaCasesClassifiedAsImported")
                        .HasColumnType("BIT")
                        .HasColumnName("ConfirmMalariaCasesClassifiedAsImported");

                    b.Property<bool?>("ConfirmMalariaCasesClassifiedAsIndigenous")
                        .HasColumnType("BIT")
                        .HasColumnName("ConfirmMalariaCasesClassifiedAsIndigenous");

                    b.Property<bool?>("ConfirmMalariaCasesClassifiedAsIntroduced")
                        .HasColumnType("BIT")
                        .HasColumnName("ConfirmMalariaCasesClassifiedAsIntroduced");

                    b.Property<bool?>("ConfirmMalariaCasesClassifiedAsLocal")
                        .HasColumnType("BIT")
                        .HasColumnName("ConfirmMalariaCasesClassifiedAsLocal");

                    b.Property<bool?>("ConfirmMalariaCasesInvestigated")
                        .HasColumnType("BIT")
                        .HasColumnName("ConfirmMalariaCasesInvestigated");

                    b.Property<bool?>("ConfirmMalariaCasesNotified")
                        .HasColumnType("BIT")
                        .HasColumnName("ConfirmMalariaCasesNotified");

                    b.Property<double?>("ConsistencyBetweenCoreVariables")
                        .HasColumnType("FLOAT")
                        .HasColumnName("ConsistencyBetweenCoreVariables");

                    b.Property<bool?>("ConsistencyOverTimeCoreIndicators")
                        .HasColumnType("BIT")
                        .HasColumnName("ConsistencyOverTimeCoreIndicators");

                    b.Property<double?>("CoreVariableCompletenessWithinRegister")
                        .HasColumnType("FLOAT")
                        .HasColumnName("CoreVariableCompletenessWithinRegister");

                    b.Property<double?>("CoreVariableCompletenessWithinReport")
                        .HasColumnType("FLOAT")
                        .HasColumnName("CoreVariableCompletenessWithinReport");

                    b.Property<double?>("CoreVariableConcordanceBtwRegister")
                        .HasColumnType("FLOAT")
                        .HasColumnName("CoreVariableConcordanceBtwRegister");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("FociInvestigationReportsTimeliness")
                        .HasColumnType("FLOAT")
                        .HasColumnName("FociInvestigationReportsTimeliness");

                    b.Property<bool>("IsFinalized")
                        .HasColumnType("BIT")
                        .HasColumnName("IsFinalized");

                    b.Property<bool?>("KeyVariableConcordanceBtwTwoReportingSystem")
                        .HasColumnType("BIT")
                        .HasColumnName("KeyVariableConcordanceBtwTwoReportingSystem");

                    b.Property<bool?>("MalariaCasesDueToPF")
                        .HasColumnType("BIT")
                        .HasColumnName("MalariaCasesDueToPF");

                    b.Property<bool?>("MalariaCasesDueToPK")
                        .HasColumnType("BIT")
                        .HasColumnName("MalariaCasesDueToPK");

                    b.Property<bool?>("MalariaCasesDueToPM")
                        .HasColumnType("BIT")
                        .HasColumnName("MalariaCasesDueToPM");

                    b.Property<bool?>("MalariaCasesDueToPO")
                        .HasColumnType("BIT")
                        .HasColumnName("MalariaCasesDueToPO");

                    b.Property<bool?>("MalariaCasesDueToPV")
                        .HasColumnType("BIT")
                        .HasColumnName("MalariaCasesDueToPV");

                    b.Property<bool?>("ReportCompleteness")
                        .HasColumnType("BIT")
                        .HasColumnName("ReportCompleteness");

                    b.Property<double?>("ReportTimeliness")
                        .HasColumnType("FLOAT")
                        .HasColumnName("ReportTimeliness");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<short>("Year")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("EliminationSummary", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.Elimination.SummaryDataQualityResultReason", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsFinalized")
                        .HasColumnType("BIT")
                        .HasColumnName("IsFinalized");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("Reason");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<short>("Year")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("EliminationSummaryDataQualityResultReason", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevel", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("FileName");

                    b.Property<DateTime>("From")
                        .HasColumnType("DATE")
                        .HasColumnName("From");

                    b.Property<bool>("IsFinalized")
                        .HasColumnType("BIT")
                        .HasColumnName("IsFinalized");

                    b.Property<DateTime>("To")
                        .HasColumnType("DATE")
                        .HasColumnName("To");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("ValidationPeriodStartDate")
                        .HasColumnType("DATETIME")
                        .HasColumnName("ValidationPeriodStartDate");

                    b.Property<short>("Version")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Version");

                    b.Property<short>("Year")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("ServiceLevel", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevelRegister", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ServiceLevelId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("ServiceLevelId");

                    b.Property<byte>("Type")
                        .HasColumnType("TINYINT")
                        .HasColumnName("Type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ServiceLevelId", "Type")
                        .IsUnique();

                    b.ToTable("ServiceLevelRegister", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevelVariable", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DQAVariableId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("DQAVariableId");

                    b.Property<Guid>("ServiceLevelId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("ServiceLevelId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("DQAVariableId");

                    b.HasIndex("ServiceLevelId", "DQAVariableId")
                        .IsUnique();

                    b.ToTable("ServiceLevelVariable", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevelVariableCompletness", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<double?>("Age")
                        .HasColumnType("FLOAT")
                        .HasColumnName("Age");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("Diagnosis")
                        .HasColumnType("FLOAT")
                        .HasColumnName("Diagnosis");

                    b.Property<bool>("IsFinalized")
                        .HasColumnType("BIT")
                        .HasColumnName("IsFinalized");

                    b.Property<string>("ObservedDataQualityResultReason")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("ObservedDataQualityResultReason");

                    b.Property<Guid>("ServiceLevelId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("ServiceLevelId");

                    b.Property<double?>("Sex")
                        .HasColumnType("FLOAT")
                        .HasColumnName("Sex");

                    b.Property<double?>("Total")
                        .HasColumnType("FLOAT")
                        .HasColumnName("Total");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ServiceLevelId");

                    b.ToTable("ServiceLevelVariableCompletness", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevelVariableData", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("ErrorInDataSources")
                        .HasColumnType("FLOAT")
                        .HasColumnName("ErrorInDataSources");

                    b.Property<double?>("MonthConcordance")
                        .HasColumnType("FLOAT")
                        .HasColumnName("MonthConcordance");

                    b.Property<Guid>("ServiceLevelVariableId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("ServiceLevelVariableId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ServiceLevelVariableId");

                    b.ToTable("ServiceLevelVariableData", "DQA");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQADataSource", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte>("DataSystem")
                        .HasColumnType("TINYINT")
                        .HasColumnName("DataSystem");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Name");

                    b.Property<string>("Name_FR")
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Name_FR");

                    b.Property<short>("Order")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Order");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("DQADataSource", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQAVariable", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<byte>("ApplicableFor")
                        .HasColumnType("TINYINT")
                        .HasColumnName("ApplicableFor");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("NVARCHAR(500)")
                        .HasColumnName("Description");

                    b.Property<string>("Description_FR")
                        .HasColumnType("NVARCHAR(500)")
                        .HasColumnName("Description_FR");

                    b.Property<bool>("IsSystemDefined")
                        .HasColumnType("BIT")
                        .HasColumnName("IsSystemDefined");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(100)")
                        .HasColumnName("Name");

                    b.Property<string>("Name_FR")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(100)")
                        .HasColumnName("Name_FR");

                    b.Property<short>("Order")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Order");

                    b.Property<byte>("Priority")
                        .HasColumnType("TINYINT")
                        .HasColumnName("Priority");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("DQAVariable", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DRCheckListIndicator", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<bool>("ConfirmationMethod")
                        .HasColumnType("bit");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Gender")
                        .HasColumnType("bit");

                    b.Property<bool>("Geography")
                        .HasColumnType("bit");

                    b.Property<bool>("HealthSector")
                        .HasColumnType("bit");

                    b.Property<bool>("IndicatorMonitored")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Name");

                    b.Property<string>("Name_FR")
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Name_FR");

                    b.Property<short>("Order")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Order");

                    b.Property<bool>("Other")
                        .HasColumnType("bit");

                    b.Property<bool>("OverFive")
                        .HasColumnType("bit");

                    b.Property<bool>("PregnantWoman")
                        .HasColumnType("bit");

                    b.Property<Guid>("StrategyId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("StrategyId");

                    b.Property<bool>("UnderFive")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("StrategyId");

                    b.ToTable("DRCheckListIndicator", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DataAnalysisReport", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<byte[]>("Content")
                        .HasColumnType("VARBINARY(MAX)")
                        .HasColumnName("Content");

                    b.Property<string>("ContentType")
                        .HasColumnType("NVARCHAR(100)")
                        .HasColumnName("ContentType");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Extension")
                        .HasColumnType("NVARCHAR(10)")
                        .HasColumnName("Extension");

                    b.Property<string>("Name")
                        .HasColumnType("NVARCHAR(200)")
                        .HasColumnName("Name");

                    b.Property<string>("Size")
                        .HasColumnType("VARCHAR(10)")
                        .HasColumnName("Size");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("DataAnalysisReport", "DataCollection");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.DRIndicator", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<bool>("AssessMalariaControlStrategies")
                        .HasColumnType("BIT")
                        .HasColumnName("AssessMalariaControlStrategies");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IndicatorId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("IndicatorId");

                    b.Property<Guid?>("SpecificMalariaControlStrategyId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("SpecificMalariaControlStrategyId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessMalariaControlStrategies");

                    b.HasIndex("IndicatorId");

                    b.HasIndex("SpecificMalariaControlStrategyId");

                    b.HasIndex("IndicatorId", "AssessMalariaControlStrategies", "SpecificMalariaControlStrategyId")
                        .IsUnique()
                        .HasFilter("[SpecificMalariaControlStrategyId] IS NOT NULL");

                    b.ToTable("DRIndicator", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.DRVariable", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .HasColumnType("BIT")
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Name");

                    b.Property<string>("Name_FR")
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Name_FR");

                    b.Property<short>("Order")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Order");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("DRVariable", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.DRVariableStrategyMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<byte?>("CaseCategory")
                        .HasColumnType("TINYINT")
                        .HasColumnName("CaseCategory");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DRVariableId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("DRVariableId");

                    b.Property<bool>("Gender")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("Gender");

                    b.Property<bool>("Geography")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("Geography");

                    b.Property<bool>("HealthSector")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("HealthSector");

                    b.Property<byte?>("OriginallyBelongsTo")
                        .HasColumnType("TINYINT")
                        .HasColumnName("OriginallyBelongsTo");

                    b.Property<bool>("Other")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("Other");

                    b.Property<bool>("OverFive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("OverFive");

                    b.Property<bool>("PregnantWoman")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("PregnantWoman");

                    b.Property<bool>("Recorded")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("Recorded");

                    b.Property<bool>("Reported")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("Reported");

                    b.Property<Guid>("StrategyId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("StrategyId");

                    b.Property<bool>("UnderFive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("UnderFive");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("DRVariableId");

                    b.HasIndex("StrategyId");

                    b.ToTable("DRVariableStrategyMapping", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.ObjectiveDiagram", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentStrategyId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentStrategyId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DataAccess")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("DataAccess");

                    b.Property<string>("DataAnalysis")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("DataAnalysis");

                    b.Property<string>("DataQualityAssurance")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("DataQualityAssurance");

                    b.Property<string>("DataRecording")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("DataRecording");

                    b.Property<string>("DataReporting")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("DataReporting");

                    b.Property<Guid>("ObjectiveId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("ObjectiveId");

                    b.Property<byte>("Status")
                        .HasColumnType("TINYINT")
                        .HasColumnName("Status");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentStrategyId");

                    b.HasIndex("ObjectiveId");

                    b.ToTable("ObjectiveDiagram", "DeskReview");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.ObjectiveDiagramFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<byte[]>("Content")
                        .HasColumnType("VARBINARY(MAX)")
                        .HasColumnName("Content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DataFlow")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("DataFlow");

                    b.Property<string>("Extension")
                        .HasColumnType("VARCHAR(10)")
                        .HasColumnName("Extension");

                    b.Property<string>("FileName")
                        .HasColumnType("NVARCHAR(200)")
                        .HasColumnName("FileName");

                    b.Property<string>("ModifyingProcess")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("ModifyingProcess");

                    b.Property<Guid>("ObjectiveDiagramId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("ObjectiveDiagramId");

                    b.Property<byte>("Order")
                        .HasColumnType("TINYINT")
                        .HasColumnName("Order");

                    b.Property<string>("PlannedChanges")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("PlannedChanges");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ObjectiveDiagramId");

                    b.ToTable("ObjectiveDiagramFile", "DeskReview");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.ResponseDocument", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentDRResponseId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentDRResponseId");

                    b.Property<byte[]>("Content")
                        .IsRequired()
                        .HasColumnType("VARBINARY(MAX)")
                        .HasColumnName("Content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Extension")
                        .IsRequired()
                        .HasColumnType("VARCHAR(10)")
                        .HasColumnName("Extension");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(200)")
                        .HasColumnName("FileName");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentDRResponseId");

                    b.ToTable("ResponseDocument", "DeskReview");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.SubObjectiveDiagram", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentStrategyId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentStrategyId");

                    b.Property<byte[]>("Content")
                        .IsRequired()
                        .HasColumnType("VARBINARY(MAX)")
                        .HasColumnName("Content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Extension")
                        .IsRequired()
                        .HasColumnType("VARCHAR(10)")
                        .HasColumnName("Extension");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(200)")
                        .HasColumnName("FileName");

                    b.Property<byte>("Order")
                        .HasColumnType("TINYINT")
                        .HasColumnName("Order");

                    b.Property<byte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<Guid>("SubObjectiveId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("SubObjectiveId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentStrategyId");

                    b.HasIndex("SubObjectiveId");

                    b.ToTable("SubObjectiveDiagram", "DeskReview");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.EmailTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<string>("Body")
                        .HasColumnType("NVARCHAR(MAX)")
                        .HasColumnName("Body");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Subject");

                    b.Property<int>("Type")
                        .HasColumnType("INT")
                        .HasColumnName("Type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("EmailTemplate", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Identity.Identity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int")
                        .HasColumnName("AccessFailedCount");

                    b.Property<string>("AuthenticatorKey")
                        .HasColumnType("NVARCHAR(50)")
                        .HasColumnName("AuthenticatorKey");

                    b.Property<bool>("ChangePassword")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(false)
                        .HasColumnName("ChangePassword");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Email")
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Email");

                    b.Property<bool>("IsDeactivated")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeactivated");

                    b.Property<string>("Language")
                        .HasMaxLength(5)
                        .HasColumnType("CHAR")
                        .HasColumnName("Language");

                    b.Property<DateTime?>("LastLoginOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastLoginOn");

                    b.Property<bool>("Locked")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(false)
                        .HasColumnName("Locked");

                    b.Property<DateTime?>("LockoutEnd")
                        .HasColumnType("datetime2")
                        .HasColumnName("LockoutEnd");

                    b.Property<string>("Mode")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(20)")
                        .HasColumnName("Mode");

                    b.Property<DateTime?>("PasswordChangedOn")
                        .HasColumnType("DATETIME")
                        .HasColumnName("PasswordChangedOn");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("PasswordHash");

                    b.Property<string>("RecoveryCodes")
                        .HasColumnType("VARCHAR(100)")
                        .HasColumnName("RecoveryCodes");

                    b.Property<bool>("ShouldChangePassword")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(false)
                        .HasColumnName("ShouldChangePassword");

                    b.Property<bool>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(false)
                        .HasColumnName("Status");

                    b.Property<string>("Theme")
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Theme");

                    b.Property<bool>("TwoFactorEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(false)
                        .HasColumnName("TwoFactorEnabled");

                    b.Property<string>("UpdatePasswordLink")
                        .HasColumnType("VARCHAR(400)")
                        .HasColumnName("UpdatePasswordLink");

                    b.Property<DateTime?>("UpdatePasswordLinkValidUntil")
                        .HasColumnType("DATETIME")
                        .HasColumnName("UpdatePasswordLinkValidUntil");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Username");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasFilter("[Email] IS NOT NULL");

                    b.ToTable("Identity", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Identity.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdentityId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("IdentityId");

                    b.Property<bool>("IsWhoAdmin")
                        .HasColumnType("BIT")
                        .HasColumnName("IsWhoAdmin");

                    b.Property<string>("Name")
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Name");

                    b.Property<string>("OrganizationName")
                        .IsRequired()
                        .HasColumnType("VARCHAR(200)")
                        .HasColumnName("OrganizationName");

                    b.Property<int>("Status")
                        .HasColumnType("INT")
                        .HasColumnName("Status");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("IdentityId")
                        .IsUnique();

                    b.ToTable("User", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Identity.UserCountryAccess", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("CountryId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("BIT")
                        .HasColumnName("IsDefault");

                    b.Property<string>("RejectionComment")
                        .HasColumnType("NVARCHAR(1000)")
                        .HasColumnName("RejectionComment");

                    b.Property<int>("Status")
                        .HasColumnType("INT")
                        .HasColumnName("Status");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("UserId");

                    b.Property<int>("UserType")
                        .HasColumnType("INT")
                        .HasColumnName("UserType");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.HasIndex("UserId");

                    b.ToTable("UserCountryAccess", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Identity.UserRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INT")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Name");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("UserRole", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Indicator", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(MAX)")
                        .HasColumnName("Description");

                    b.Property<string>("Description_FR")
                        .HasColumnType("NVARCHAR(550)")
                        .HasColumnName("Description_FR");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(1000)")
                        .HasColumnName("Name");

                    b.Property<string>("Name_FR")
                        .HasColumnType("NVARCHAR(1000)")
                        .HasColumnName("Name_FR");

                    b.Property<int>("Order")
                        .HasColumnType("INT")
                        .HasColumnName("Order");

                    b.Property<string>("Sequence")
                        .IsRequired()
                        .HasColumnType("VARCHAR(20)")
                        .HasColumnName("Sequence");

                    b.Property<string>("ShortDescription")
                        .HasColumnType("NVARCHAR(550)")
                        .HasColumnName("ShortDescription");

                    b.Property<string>("ShortDescription_FR")
                        .HasColumnType("NVARCHAR(MAX)")
                        .HasColumnName("ShortDescription_FR");

                    b.Property<Guid>("SubObjectiveId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("SubObjectiveId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SubObjectiveId");

                    b.ToTable("Indicator", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.IndicatorStrategyMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IndicatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("IndicatorId");

                    b.Property<int>("IndicatorPriority")
                        .HasColumnType("INT")
                        .HasColumnName("IndicatorPriority");

                    b.Property<Guid?>("ParentCaseStrategyId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ParentCaseStrategyId");

                    b.Property<Guid>("StrategyId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("StrategyId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("IndicatorId");

                    b.HasIndex("ParentCaseStrategyId");

                    b.HasIndex("StrategyId");

                    b.HasIndex("IndicatorId", "StrategyId", "ParentCaseStrategyId")
                        .IsUnique()
                        .HasFilter("[ParentCaseStrategyId] IS NOT NULL");

                    b.ToTable("IndicatorStrategyMapping", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Objective", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(1000)")
                        .HasColumnName("Name");

                    b.Property<string>("Name_FR")
                        .HasColumnType("NVARCHAR(1000)")
                        .HasColumnName("Name_FR");

                    b.Property<int>("Order")
                        .HasColumnType("INT")
                        .HasColumnName("Order");

                    b.Property<string>("Sequence")
                        .IsRequired()
                        .HasColumnType("VARCHAR(20)")
                        .HasColumnName("Sequence");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("Objective", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.AssessmentHealthFacilityMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("HealthFacilityId")
                        .HasColumnType("INT")
                        .HasColumnName("HealthFacilityId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.HasIndex("HealthFacilityId");

                    b.ToTable("AssessmentHealthFacilityMapping", "Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.AssessmentQuestion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentRespondentTypeId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentRespondentTypeId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiedQuestion")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("ModifiedQuestion");

                    b.Property<Guid>("QuestionId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("QuestionId");

                    b.Property<string>("ResponseOptions")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("ResponseOptions");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentRespondentTypeId");

                    b.HasIndex("QuestionId");

                    b.ToTable("AssessmentQuestion", "QuestionBank");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.AssessmentRespondentType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool?>("HasSelfAssessmentQuestions")
                        .HasColumnType("BIT")
                        .HasColumnName("HasSelfAssessmentQuestions");

                    b.Property<bool>("IsFinalized")
                        .HasColumnType("BIT")
                        .HasColumnName("IsFinalized");

                    b.Property<byte>("RespondentType")
                        .HasColumnType("TINYINT")
                        .HasColumnName("RespondentType");

                    b.Property<short>("ShellTableFileVersion")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("ShellTableFileVersion");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("AssessmentRespondentType", "QuestionBank");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.HealthFacility", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INT")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("Code");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("CountryId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("District")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("District");

                    b.Property<string>("DistrictCode")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("DistrictCode");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("VARCHAR(300)")
                        .HasColumnName("FileName");

                    b.Property<bool>("IsObsolete")
                        .HasColumnType("BIT")
                        .HasColumnName("IsObsolete");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Name");

                    b.Property<string>("Region")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(255)")
                        .HasColumnName("Region");

                    b.Property<byte>("Type")
                        .HasColumnType("TINYINT")
                        .HasColumnName("Type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.ToTable("HealthFacility", "QuestionBank");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.QBDependentStrategyMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("QBQueIndicatorStrategyMappingId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("QBQueIndicatorStrategyMappingId");

                    b.Property<Guid>("StrategyId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("StrategyId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("QBQueIndicatorStrategyMappingId");

                    b.HasIndex("StrategyId");

                    b.ToTable("QBDependentStrategyMapping", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.QBQuestion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<bool>("CanEditOptions")
                        .HasColumnType("BIT")
                        .HasColumnName("CanEditOptions");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("Code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("ForSelfAssessment")
                        .HasColumnType("BIT")
                        .HasColumnName("ForSelfAssessment");

                    b.Property<bool>("IsMandatory")
                        .HasColumnType("BIT")
                        .HasColumnName("IsMandatory");

                    b.Property<string>("Notes")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("Notes");

                    b.Property<string>("Notes_FR")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("Notes_FR");

                    b.Property<string>("Options")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("Options");

                    b.Property<string>("Options_FR")
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("Options_FR");

                    b.Property<short>("Order")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Order");

                    b.Property<string>("Question")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("Question");

                    b.Property<string>("Question_FR")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(2000)")
                        .HasColumnName("Question_FR");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("QBQuestion", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.QBQuestionIndicatorStrategyMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IndicatorId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("IndicatorId");

                    b.Property<bool>("IsDependOnOtherStrategy")
                        .HasColumnType("BIT")
                        .HasColumnName("IsDependOnOtherStrategy");

                    b.Property<Guid>("QBQuestionId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("QBQuestionId");

                    b.Property<Guid>("StrategyId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("StrategyId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("IndicatorId");

                    b.HasIndex("QBQuestionId");

                    b.HasIndex("StrategyId");

                    b.ToTable("QBQuestionIndicatorStrategyMapping", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.QBQuestionRespondentTypeMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("QBQuestionId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("QBQuestionId");

                    b.Property<byte>("RespondentType")
                        .HasColumnType("TINYINT")
                        .HasColumnName("RespondentType");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("QBQuestionId");

                    b.ToTable("QBQuestionRespondentTypeMapping", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.ShellTable", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentRespondentTypeId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentRespondentTypeId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DistrictCode")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("DistrictCode");

                    b.Property<int?>("HealthFacilityId")
                        .HasColumnType("INT")
                        .HasColumnName("HealthFacilityId");

                    b.Property<string>("Option")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(1000)")
                        .HasColumnName("Option");

                    b.Property<byte?>("OptionOrder")
                        .HasColumnType("TINYINT")
                        .HasColumnName("OptionOrder");

                    b.Property<int>("ShellTableQuestionBankMappingId")
                        .HasColumnType("INT")
                        .HasColumnName("ShellTableQuestionBankMappingId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("Value")
                        .HasColumnType("INT")
                        .HasColumnName("Value");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentRespondentTypeId");

                    b.HasIndex("HealthFacilityId");

                    b.HasIndex("ShellTableQuestionBankMappingId");

                    b.ToTable("ShellTable", "QuestionBank");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.ShellTableQuestionBankMapping", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INT")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<short>("Order")
                        .HasColumnType("SMALLINT")
                        .HasColumnName("Order");

                    b.Property<string>("QuestionBankQuestionCode")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("QuestionBankQuestionCode");

                    b.Property<string>("ShellTableExcelTemplateSetting")
                        .HasColumnType("VARCHAR(MAX)")
                        .HasColumnName("ShellTableExcelTemplateSetting");

                    b.Property<string>("ShellTableQuestion")
                        .IsRequired()
                        .HasColumnType("VARCHAR(2000)")
                        .HasColumnName("ShellTableQuestion");

                    b.Property<byte?>("ShellTableQuestionCalculationResultType")
                        .HasColumnType("TINYINT")
                        .HasColumnName("ShellTableQuestionCalculationResultType");

                    b.Property<string>("ShellTableQuestionCode")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("ShellTableQuestionCode");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ShellTableQuestionCode", "QuestionBankQuestionCode")
                        .IsUnique();

                    b.ToTable("ShellTableQuestionBankMapping", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Region", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name_FR")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("Region", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.ScoreCard", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IndicatorId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("IndicatorId");

                    b.Property<bool>("IsFinalized")
                        .HasColumnType("BIT")
                        .HasColumnName("IsFinalized");

                    b.Property<byte?>("MetNotMetStatus")
                        .HasColumnType("TINYINT")
                        .HasColumnName("MetNotMetStatus");

                    b.Property<string>("ReasonForResult")
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("ReasonForResult");

                    b.Property<string>("Recommendation")
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("Recommendation");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.HasIndex("IndicatorId");

                    b.ToTable("ScoreCard", "Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.ShellTableReport", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("AssessmentId");

                    b.Property<byte[]>("Content")
                        .HasColumnType("VARBINARY(MAX)")
                        .HasColumnName("Content");

                    b.Property<string>("ContentType")
                        .HasColumnType("NVARCHAR(100)")
                        .HasColumnName("ContentType");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Extension")
                        .HasColumnType("NVARCHAR(10)")
                        .HasColumnName("Extension");

                    b.Property<string>("Name")
                        .HasColumnType("NVARCHAR(200)")
                        .HasColumnName("Name");

                    b.Property<string>("Size")
                        .HasColumnType("VARCHAR(10)")
                        .HasColumnName("Size");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("ShellTableReport", "QuestionBank");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Strategy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(1000)")
                        .HasColumnName("Name");

                    b.Property<string>("Name_FR")
                        .HasColumnType("NVARCHAR(1000)")
                        .HasColumnName("Name_FR");

                    b.Property<int>("Order")
                        .HasColumnType("INT")
                        .HasColumnName("Order");

                    b.Property<string>("ShortName")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(100)")
                        .HasColumnName("ShortName");

                    b.Property<int>("Type")
                        .HasColumnType("INT")
                        .HasColumnName("Type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("Strategy", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.SubObjective", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true)
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(1000)")
                        .HasColumnName("Name");

                    b.Property<string>("Name_FR")
                        .HasColumnType("NVARCHAR(1000)")
                        .HasColumnName("Name_FR");

                    b.Property<Guid>("ObjectiveId")
                        .HasColumnType("UNIQUEIDENTIFIER")
                        .HasColumnName("ObjectiveId");

                    b.Property<int>("Order")
                        .HasColumnType("INT")
                        .HasColumnName("Order");

                    b.Property<string>("Sequence")
                        .IsRequired()
                        .HasColumnType("VARCHAR(20)")
                        .HasColumnName("Sequence");

                    b.Property<DateTime>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ObjectiveId");

                    b.ToTable("SubObjective", "Internal");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AnalyticalOutputIndicatorStrategyMapping", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Indicator", "Indicator")
                        .WithMany("AnalyticalOutputIndicatorMapping")
                        .HasForeignKey("IndicatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Strategy", "Strategy")
                        .WithMany("AnalyticalOutputStrategyMappings")
                        .HasForeignKey("StrategyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Indicator");

                    b.Navigation("Strategy");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Assessment", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Country", "Country")
                        .WithMany("Assessments")
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Country");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentDRResponse", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.AssessmentIndicator", "AssessmentIndicator")
                        .WithMany("AssessmentDeskReviewResponses")
                        .HasForeignKey("AssessmentIndicatorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.AssessmentStrategy", "AssessmentStrategy")
                        .WithMany("AssessmentDeskReviewResponses")
                        .HasForeignKey("AssessmentStrategyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("AssessmentIndicator");

                    b.Navigation("AssessmentStrategy");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentIndicator", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("AssessmentIndicators")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Indicator", "Indicator")
                        .WithMany("AssessmentIndicators")
                        .HasForeignKey("IndicatorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Assessment");

                    b.Navigation("Indicator");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentStatus", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("AssessmentStatuses")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentStrategy", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("AssessmentStrategies")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Strategy", "Strategy")
                        .WithMany("AssessmentStrategies")
                        .HasForeignKey("StrategyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Assessment");

                    b.Navigation("Strategy");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentUser", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("AssessmentUsers")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Identity.User", "User")
                        .WithMany("AssessmentUsers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");

                    b.Navigation("User");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Country", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Region", "Region")
                        .WithMany("Countries")
                        .HasForeignKey("RegionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Region");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DQAIndicator", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Indicator", "Indicator")
                        .WithMany("DQAIndicators")
                        .HasForeignKey("IndicatorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Indicator");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DQAVariableStrategyMapping", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.DQAVariable", "DQAVariable")
                        .WithMany("DQAVariableStrategies")
                        .HasForeignKey("DQAVariableId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Strategy", "Strategy")
                        .WithMany("DQAVariableStrategies")
                        .HasForeignKey("StrategyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DQAVariable");

                    b.Navigation("Strategy");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.DataSource", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.DQADataSource", "DQADataSource")
                        .WithMany("DataSources")
                        .HasForeignKey("DataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.DQA.DeskLevel.DeskLevel", "DeskLevel")
                        .WithMany("DataSources")
                        .HasForeignKey("DeskLevelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DQADataSource");

                    b.Navigation("DeskLevel");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.DeskLevel", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("DeskLevels")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.DeskLevelDataSystem1", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("DeskLevelDataSystem1Entity")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.DeskLevelDataSystem2", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("DeskLevelDataSystem2Entity")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.Summary", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("Summaries")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.SummaryDataQualityResultReason", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("SummaryDataQualityResultReasons")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.VariableMapping", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.DQA.DeskLevel.DeskLevel", "DeskLevel")
                        .WithMany("VariableMappings")
                        .HasForeignKey("DeskLevelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.DQAVariable", "DQAVariable")
                        .WithMany("VariableMappings")
                        .HasForeignKey("VariableId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DQAVariable");

                    b.Navigation("DeskLevel");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.Elimination.Summary", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("EliminationSummaries")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.Elimination.SummaryDataQualityResultReason", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("EliminationSummaryDataQualityResultReasons")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevel", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("ServiceLevel")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevelRegister", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevel", "ServiceLevel")
                        .WithMany("ServiceLevelRegisters")
                        .HasForeignKey("ServiceLevelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ServiceLevel");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevelVariable", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.DQAVariable", "DQAVariable")
                        .WithMany()
                        .HasForeignKey("DQAVariableId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevel", "ServiceLevel")
                        .WithMany("ServiceLevelVariables")
                        .HasForeignKey("ServiceLevelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DQAVariable");

                    b.Navigation("ServiceLevel");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevelVariableCompletness", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevel", "ServiceLevel")
                        .WithMany("ServiceLevelVariableCompletnesses")
                        .HasForeignKey("ServiceLevelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ServiceLevel");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevelVariableData", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevelVariable", "ServiceLevelVariable")
                        .WithMany("ServiceLevelVariableData")
                        .HasForeignKey("ServiceLevelVariableId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ServiceLevelVariable");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DRCheckListIndicator", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Strategy", "Strategy")
                        .WithMany()
                        .HasForeignKey("StrategyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Strategy");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DataAnalysisReport", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("DataAnalysisReports")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.DRIndicator", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Indicator", "Indicator")
                        .WithMany("DRIndicators")
                        .HasForeignKey("IndicatorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Strategy", "SpecificMalariaControlStrategy")
                        .WithMany("DRIndicators")
                        .HasForeignKey("SpecificMalariaControlStrategyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Indicator");

                    b.Navigation("SpecificMalariaControlStrategy");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.DRVariableStrategyMapping", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.DeskReview.DRVariable", "DRVariable")
                        .WithMany("DRVariableStrategies")
                        .HasForeignKey("DRVariableId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Strategy", "Strategy")
                        .WithMany("DRVariableStrategies")
                        .HasForeignKey("StrategyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DRVariable");

                    b.Navigation("Strategy");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.ObjectiveDiagram", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.AssessmentStrategy", "AssessmentStrategy")
                        .WithMany("ObjectiveDiagrams")
                        .HasForeignKey("AssessmentStrategyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Objective", "Objective")
                        .WithMany("ObjectiveDiagrams")
                        .HasForeignKey("ObjectiveId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssessmentStrategy");

                    b.Navigation("Objective");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.ObjectiveDiagramFile", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.DeskReview.ObjectiveDiagram", "ObjectiveDiagram")
                        .WithMany("ObjectiveDiagramFiles")
                        .HasForeignKey("ObjectiveDiagramId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ObjectiveDiagram");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.ResponseDocument", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.AssessmentDRResponse", "AssessmentDRResponse")
                        .WithMany("ResponseDocuments")
                        .HasForeignKey("AssessmentDRResponseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssessmentDRResponse");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.SubObjectiveDiagram", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.AssessmentStrategy", "AssessmentStrategy")
                        .WithMany("SubObjectiveDiagrams")
                        .HasForeignKey("AssessmentStrategyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.SubObjective", "SubObjective")
                        .WithMany("SubObjectiveDiagrams")
                        .HasForeignKey("SubObjectiveId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssessmentStrategy");

                    b.Navigation("SubObjective");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Identity.User", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Identity.Identity", "Identity")
                        .WithOne("User")
                        .HasForeignKey("WHO.MALARIA.Domain.Models.Identity.User", "IdentityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Identity");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Identity.UserCountryAccess", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Country", "Country")
                        .WithMany("UserCountryAccesses")
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Identity.User", "User")
                        .WithMany("UserCountryAccesses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");

                    b.Navigation("User");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Indicator", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.SubObjective", "SubObjective")
                        .WithMany("Indicators")
                        .HasForeignKey("SubObjectiveId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("SubObjective");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.IndicatorStrategyMapping", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Indicator", "Indicator")
                        .WithMany("IndicatorStrategies")
                        .HasForeignKey("IndicatorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Strategy", "ParentCaseStrategy")
                        .WithMany("IndicatorCaseStrategies")
                        .HasForeignKey("ParentCaseStrategyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("WHO.MALARIA.Domain.Models.Strategy", "Strategy")
                        .WithMany("IndicatorOtherStrategies")
                        .HasForeignKey("StrategyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Indicator");

                    b.Navigation("ParentCaseStrategy");

                    b.Navigation("Strategy");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.AssessmentHealthFacilityMapping", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("AssessmentHealthFacilityMappings")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.QuestionBank.HealthFacility", "HealthFacility")
                        .WithMany("AssessmentHealthFacilityMappings")
                        .HasForeignKey("HealthFacilityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");

                    b.Navigation("HealthFacility");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.AssessmentQuestion", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.QuestionBank.AssessmentRespondentType", "AssessmentRespondentType")
                        .WithMany("AssessmentQBQuestionMappings")
                        .HasForeignKey("AssessmentRespondentTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.QuestionBank.QBQuestion", "Question")
                        .WithMany("AssessmentQBQuestionMappings")
                        .HasForeignKey("QuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssessmentRespondentType");

                    b.Navigation("Question");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.AssessmentRespondentType", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("AssessmentRespondentTypeQBQuestions")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.HealthFacility", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Country", "Country")
                        .WithMany("HealthFacilities")
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.QBDependentStrategyMapping", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.QuestionBank.QBQuestionIndicatorStrategyMapping", "QBQueIndicatorStrategyMapping")
                        .WithMany("QBDependentStrategyMappings")
                        .HasForeignKey("QBQueIndicatorStrategyMappingId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Strategy", "Strategy")
                        .WithMany("QBDependentStrategyMappings")
                        .HasForeignKey("StrategyId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("QBQueIndicatorStrategyMapping");

                    b.Navigation("Strategy");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.QBQuestionIndicatorStrategyMapping", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Indicator", "Indicator")
                        .WithMany("QBQuestionIndicatorMappings")
                        .HasForeignKey("IndicatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.QuestionBank.QBQuestion", "QBQuestion")
                        .WithMany("QBQuestionIndicatorStrategyMappings")
                        .HasForeignKey("QBQuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Strategy", "Strategy")
                        .WithMany("QBQuestionIndicatorStrategies")
                        .HasForeignKey("StrategyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Indicator");

                    b.Navigation("QBQuestion");

                    b.Navigation("Strategy");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.QBQuestionRespondentTypeMapping", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.QuestionBank.QBQuestion", "QBQuestion")
                        .WithMany("QBQuestionRespondentTypeMappings")
                        .HasForeignKey("QBQuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("QBQuestion");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.ShellTable", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.QuestionBank.AssessmentRespondentType", "AssessmentRespondentType")
                        .WithMany("ShellTables")
                        .HasForeignKey("AssessmentRespondentTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.QuestionBank.HealthFacility", "HealthFacility")
                        .WithMany("ShellTables")
                        .HasForeignKey("HealthFacilityId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("WHO.MALARIA.Domain.Models.QuestionBank.ShellTableQuestionBankMapping", "ShellTableQuestionBankMapping")
                        .WithMany("ShellTables")
                        .HasForeignKey("ShellTableQuestionBankMappingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssessmentRespondentType");

                    b.Navigation("HealthFacility");

                    b.Navigation("ShellTableQuestionBankMapping");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.ScoreCard", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("ScoreCards")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WHO.MALARIA.Domain.Models.Indicator", "Indicator")
                        .WithMany("ScoreCards")
                        .HasForeignKey("IndicatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");

                    b.Navigation("Indicator");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.ShellTableReport", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Assessment", "Assessment")
                        .WithMany("ShellTableReports")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.SubObjective", b =>
                {
                    b.HasOne("WHO.MALARIA.Domain.Models.Objective", "Objective")
                        .WithMany("SubObjectives")
                        .HasForeignKey("ObjectiveId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Objective");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Assessment", b =>
                {
                    b.Navigation("AssessmentHealthFacilityMappings");

                    b.Navigation("AssessmentIndicators");

                    b.Navigation("AssessmentRespondentTypeQBQuestions");

                    b.Navigation("AssessmentStatuses");

                    b.Navigation("AssessmentStrategies");

                    b.Navigation("AssessmentUsers");

                    b.Navigation("DataAnalysisReports");

                    b.Navigation("DeskLevelDataSystem1Entity");

                    b.Navigation("DeskLevelDataSystem2Entity");

                    b.Navigation("DeskLevels");

                    b.Navigation("EliminationSummaries");

                    b.Navigation("EliminationSummaryDataQualityResultReasons");

                    b.Navigation("ScoreCards");

                    b.Navigation("ServiceLevel");

                    b.Navigation("ShellTableReports");

                    b.Navigation("Summaries");

                    b.Navigation("SummaryDataQualityResultReasons");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentDRResponse", b =>
                {
                    b.Navigation("ResponseDocuments");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentIndicator", b =>
                {
                    b.Navigation("AssessmentDeskReviewResponses");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.AssessmentStrategy", b =>
                {
                    b.Navigation("AssessmentDeskReviewResponses");

                    b.Navigation("ObjectiveDiagrams");

                    b.Navigation("SubObjectiveDiagrams");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Country", b =>
                {
                    b.Navigation("Assessments");

                    b.Navigation("HealthFacilities");

                    b.Navigation("UserCountryAccesses");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.DeskLevel.DeskLevel", b =>
                {
                    b.Navigation("DataSources");

                    b.Navigation("VariableMappings");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevel", b =>
                {
                    b.Navigation("ServiceLevelRegisters");

                    b.Navigation("ServiceLevelVariableCompletnesses");

                    b.Navigation("ServiceLevelVariables");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQA.ServiceLevel.ServiceLevelVariable", b =>
                {
                    b.Navigation("ServiceLevelVariableData");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQADataSource", b =>
                {
                    b.Navigation("DataSources");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DQAVariable", b =>
                {
                    b.Navigation("DQAVariableStrategies");

                    b.Navigation("VariableMappings");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.DRVariable", b =>
                {
                    b.Navigation("DRVariableStrategies");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.DeskReview.ObjectiveDiagram", b =>
                {
                    b.Navigation("ObjectiveDiagramFiles");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Identity.Identity", b =>
                {
                    b.Navigation("User");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Identity.User", b =>
                {
                    b.Navigation("AssessmentUsers");

                    b.Navigation("UserCountryAccesses");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Indicator", b =>
                {
                    b.Navigation("AnalyticalOutputIndicatorMapping");

                    b.Navigation("AssessmentIndicators");

                    b.Navigation("DQAIndicators");

                    b.Navigation("DRIndicators");

                    b.Navigation("IndicatorStrategies");

                    b.Navigation("QBQuestionIndicatorMappings");

                    b.Navigation("ScoreCards");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Objective", b =>
                {
                    b.Navigation("ObjectiveDiagrams");

                    b.Navigation("SubObjectives");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.AssessmentRespondentType", b =>
                {
                    b.Navigation("AssessmentQBQuestionMappings");

                    b.Navigation("ShellTables");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.HealthFacility", b =>
                {
                    b.Navigation("AssessmentHealthFacilityMappings");

                    b.Navigation("ShellTables");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.QBQuestion", b =>
                {
                    b.Navigation("AssessmentQBQuestionMappings");

                    b.Navigation("QBQuestionIndicatorStrategyMappings");

                    b.Navigation("QBQuestionRespondentTypeMappings");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.QBQuestionIndicatorStrategyMapping", b =>
                {
                    b.Navigation("QBDependentStrategyMappings");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.QuestionBank.ShellTableQuestionBankMapping", b =>
                {
                    b.Navigation("ShellTables");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Region", b =>
                {
                    b.Navigation("Countries");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.Strategy", b =>
                {
                    b.Navigation("AnalyticalOutputStrategyMappings");

                    b.Navigation("AssessmentStrategies");

                    b.Navigation("DQAVariableStrategies");

                    b.Navigation("DRIndicators");

                    b.Navigation("DRVariableStrategies");

                    b.Navigation("IndicatorCaseStrategies");

                    b.Navigation("IndicatorOtherStrategies");

                    b.Navigation("QBDependentStrategyMappings");

                    b.Navigation("QBQuestionIndicatorStrategies");
                });

            modelBuilder.Entity("WHO.MALARIA.Domain.Models.SubObjective", b =>
                {
                    b.Navigation("Indicators");

                    b.Navigation("SubObjectiveDiagrams");
                });
#pragma warning restore 612, 618
        }
    }
}
