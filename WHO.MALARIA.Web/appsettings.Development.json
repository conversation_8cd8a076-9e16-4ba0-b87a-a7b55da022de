{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AppSettings": {"ShowErrorDetails": true, "RateLimiting": {"MaxRequests": 1000, "TimeWindowMinutes": 1, "EnableGlobalRateLimit": true, "EnablePerEndpointRateLimit": true, "EndpointLimits": {"POST:/api/auth/login": {"MaxRequests": 10, "TimeWindowMinutes": 15}, "POST:/api/auth/register": {"MaxRequests": 5, "TimeWindowMinutes": 60}, "POST:/api/auth/forgot-password": {"MaxRequests": 5, "TimeWindowMinutes": 60}, "POST:/api/ratelimittest/endpoint-specific": {"MaxRequests": 5, "TimeWindowMinutes": 1}, "POST:/api/ratelimittest/heavy-operation": {"MaxRequests": 3, "TimeWindowMinutes": 1}}}}}