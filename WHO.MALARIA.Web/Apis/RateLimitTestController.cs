using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace WHO.MALARIA.Web.Apis
{
    /// <summary>
    /// Test controller for verifying rate limiting functionality
    /// This controller should be removed in production
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class RateLimitTestController : BaseApiController
    {
        private readonly ILogger<RateLimitTestController> _logger;

        public RateLimitTestController(ILogger<RateLimitTestController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Test endpoint for verifying global rate limiting
        /// </summary>
        /// <returns>Success response with timestamp</returns>
        [HttpGet("global")]
        public IActionResult TestGlobalRateLimit()
        {
            _logger.LogInformation("Global rate limit test endpoint called at {Timestamp}", DateTime.UtcNow);
            
            return Ok(new
            {
                message = "Global rate limit test successful",
                timestamp = DateTime.UtcNow,
                endpoint = "GET:/api/ratelimittest/global"
            });
        }

        /// <summary>
        /// Test endpoint for verifying per-endpoint rate limiting
        /// This endpoint should have stricter limits if configured
        /// </summary>
        /// <returns>Success response with timestamp</returns>
        [HttpPost("endpoint-specific")]
        public IActionResult TestEndpointSpecificRateLimit([FromBody] object data = null)
        {
            _logger.LogInformation("Endpoint-specific rate limit test called at {Timestamp}", DateTime.UtcNow);
            
            return Ok(new
            {
                message = "Endpoint-specific rate limit test successful",
                timestamp = DateTime.UtcNow,
                endpoint = "POST:/api/ratelimittest/endpoint-specific",
                data = data
            });
        }

        /// <summary>
        /// Test endpoint that simulates a resource-intensive operation
        /// </summary>
        /// <returns>Success response after delay</returns>
        [HttpPost("heavy-operation")]
        public async Task<IActionResult> TestHeavyOperation()
        {
            _logger.LogInformation("Heavy operation test called at {Timestamp}", DateTime.UtcNow);
            
            // Simulate a heavy operation
            await Task.Delay(100);
            
            return Ok(new
            {
                message = "Heavy operation completed",
                timestamp = DateTime.UtcNow,
                endpoint = "POST:/api/ratelimittest/heavy-operation",
                processingTime = "100ms"
            });
        }

        /// <summary>
        /// Get current rate limit status for the client
        /// </summary>
        /// <returns>Rate limit information from headers</returns>
        [HttpGet("status")]
        public IActionResult GetRateLimitStatus()
        {
            var headers = HttpContext.Response.Headers;
            
            return Ok(new
            {
                message = "Rate limit status",
                timestamp = DateTime.UtcNow,
                clientIp = HttpContext.Connection.RemoteIpAddress?.ToString(),
                forwardedFor = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault(),
                realIp = HttpContext.Request.Headers["X-Real-IP"].FirstOrDefault(),
                rateLimitHeaders = new
                {
                    limit = headers.ContainsKey("X-RateLimit-Limit") ? headers["X-RateLimit-Limit"].ToString() : "Not set",
                    remaining = headers.ContainsKey("X-RateLimit-Remaining") ? headers["X-RateLimit-Remaining"].ToString() : "Not set",
                    reset = headers.ContainsKey("X-RateLimit-Reset") ? headers["X-RateLimit-Reset"].ToString() : "Not set"
                }
            });
        }
    }
}
