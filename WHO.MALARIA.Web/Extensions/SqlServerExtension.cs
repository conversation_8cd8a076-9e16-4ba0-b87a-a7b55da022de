﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Web.Extensions
{
    internal static class SqlServerExtension
    {
        internal static IServiceCollection AddSqlDbContext(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddDbContext<MalariaDbContext>(options =>
            {
                string connectionString = configuration.GetConnectionString(Constants.Startup.ConnectionString);

                options.UseSqlServer(connectionString, 
                    sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure(maxRetryCount: 3, maxRetryDelay: TimeSpan.FromSeconds(30), errorNumbersToAdd: null);
                        sqlOptions.MigrationsAssembly("WHO.MALARIA.Database");
                    });
            });

            return services;
        }
    }
}