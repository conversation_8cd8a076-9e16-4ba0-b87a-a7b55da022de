using Microsoft.AspNetCore.RateLimiting;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.RateLimiting;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Web.Extensions
{
    /// <summary>
    /// Extension methods for setting up rate limiting services
    /// </summary>
    public static class RateLimitingSetupExtension
    {
        /// <summary>
        /// Adds rate limiting services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="appSettings">Application settings containing rate limiting configuration</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddRateLimitingServices(this IServiceCollection services, AppSettings appSettings)
        {
            if (appSettings?.RateLimiting?.EnableGlobalRateLimit == true)
            {
                services.AddRateLimiter(options =>
                {
                    // Global rate limiting policy
                    options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(httpContext =>
                        RateLimitPartition.GetFixedWindowLimiter(
                            partitionKey: GetClientIdentifier(httpContext),
                            factory: partition => new FixedWindowRateLimiterOptions
                            {
                                AutoReplenishment = true,
                                PermitLimit = appSettings.RateLimiting.MaxRequests,
                                Window = TimeSpan.FromMinutes(appSettings.RateLimiting.TimeWindowMinutes)
                            }));

                    // Add specific endpoint policies if per-endpoint rate limiting is enabled
                    if (appSettings.RateLimiting.EnablePerEndpointRateLimit && 
                        appSettings.RateLimiting.EndpointLimits?.Any() == true)
                    {
                        foreach (var endpointLimit in appSettings.RateLimiting.EndpointLimits)
                        {
                            var policyName = endpointLimit.Key.Replace(":", "_").Replace("/", "_");
                            
                            options.AddPolicy(policyName, httpContext =>
                                RateLimitPartition.GetFixedWindowLimiter(
                                    partitionKey: GetClientIdentifier(httpContext),
                                    factory: partition => new FixedWindowRateLimiterOptions
                                    {
                                        AutoReplenishment = true,
                                        PermitLimit = endpointLimit.Value.MaxRequests,
                                        Window = TimeSpan.FromMinutes(endpointLimit.Value.TimeWindowMinutes)
                                    }));
                        }
                    }

                    // Configure rejection response
                    options.RejectionStatusCode = 429;
                    options.OnRejected = async (context, token) =>
                    {
                        context.HttpContext.Response.StatusCode = 429;
                        context.HttpContext.Response.ContentType = "application/json";
                        
                        var response = new
                        {
                            error = "Rate limit exceeded",
                            message = "Too many requests. Please try again later.",
                            statusCode = 429
                        };

                        await context.HttpContext.Response.WriteAsync(
                            System.Text.Json.JsonSerializer.Serialize(response), token);
                    };
                });
            }

            return services;
        }

        /// <summary>
        /// Gets a client identifier for rate limiting purposes
        /// </summary>
        /// <param name="httpContext">The HTTP context</param>
        /// <returns>A string identifying the client</returns>
        private static string GetClientIdentifier(HttpContext httpContext)
        {
            // Try to get real IP from forwarded headers (for load balancers/proxies)
            var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                var firstIp = forwardedFor.Split(',')[0].Trim();
                if (System.Net.IPAddress.TryParse(firstIp, out _))
                    return firstIp;
            }

            var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp) && System.Net.IPAddress.TryParse(realIp, out _))
                return realIp;

            // Fallback to connection remote IP
            return httpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        }
    }
}
