using Microsoft.Extensions.DependencyInjection;
using System.Threading.RateLimiting;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Web.Extensions
{
    /// <summary>
    /// Extension methods for setting up rate limiting services
    /// </summary>
    public static class RateLimitingSetupExtension
    {
        /// <summary>
        /// Adds rate limiting services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="appSettings">Application settings containing rate limiting configuration</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddRateLimitingServices(this IServiceCollection services, AppSettings appSettings)
        {
            // For .NET 8, we'll rely on our custom middleware for rate limiting
            // The built-in rate limiting is available but our custom implementation provides more flexibility
            return services;
        }

        /// <summary>
        /// Gets a client identifier for rate limiting purposes
        /// </summary>
        /// <param name="httpContext">The HTTP context</param>
        /// <returns>A string identifying the client</returns>
        private static string GetClientIdentifier(HttpContext httpContext)
        {
            // Try to get real IP from forwarded headers (for load balancers/proxies)
            var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                var firstIp = forwardedFor.Split(',')[0].Trim();
                if (System.Net.IPAddress.TryParse(firstIp, out _))
                    return firstIp;
            }

            var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp) && System.Net.IPAddress.TryParse(realIp, out _))
                return realIp;

            // Fallback to connection remote IP
            return httpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        }
    }
}
