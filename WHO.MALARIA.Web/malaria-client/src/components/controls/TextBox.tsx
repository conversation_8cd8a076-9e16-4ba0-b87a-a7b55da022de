﻿import React from "react";
import {
  FilledTextFieldProps,
  OutlinedTextFieldProps,
  StandardTextFieldProps,
  TextField,
} from "@mui/material";

export interface ITextBoxProps {
  maxLength?: number;
}
export type TextFieldProps = (
  | StandardTextFieldProps
  | FilledTextFieldProps
  | OutlinedTextFieldProps
) &
  ITextBoxProps;

/** Renders HTML TextBox i.e. <input type="text" /> */
const TextBox = (props: TextFieldProps) => {
  const { className, placeholder, error, maxLength, value, ...textFieldProps } = props;

  return (
    <TextField
      variant="outlined"
      InputLabelProps={
        {
          //shrink: label ? true : false
        }
      }
      className={`${
        error
          ? className
            ? className + " who-error"
            : "col-form-control app-error"
          : className
          ? className
          : "col-form-control inputfocus"
      } `}
      inputProps={{
        maxLength: maxLength && maxLength,
        placeholder: placeholder || "",
        min: 0,
      }}
      value={value === null ? "" : value}
      {...textFieldProps}
    />
  );
};

export default TextBox;
