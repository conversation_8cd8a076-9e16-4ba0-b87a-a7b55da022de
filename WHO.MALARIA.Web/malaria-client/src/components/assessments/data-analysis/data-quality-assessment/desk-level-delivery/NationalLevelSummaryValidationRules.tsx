import { Constants } from "../../../../../models/Constants";
import { DataType } from "../../../../../models/Enums";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../models/ValidationRuleModel";

/** Summary DQA validation rules */
const NationalLevelSummaryValidationRules: IValidationRuleProvider = {
  dataQualityResultReason: new ValidationRuleModel(DataType.String, true),
  // Core indicators - allow null values (NA selections) by setting required=false
  malariaOutpatientProportion: new ValidationRuleModel(DataType.Boolean, false),
  malariaInPatientProportion: new ValidationRuleModel(DataType.Boolean, false),
  malariaInPatientDeathProportion: new ValidationRuleModel(
    DataType.Boolean,
    false
  ),
  testPositivityRate: new ValidationRuleModel(DataType.Boolean, false),
  slidePositivityRate: new ValidationRuleModel(DataType.Boolean, false),
  rdtPositivityRate: new ValidationRuleModel(DataType.Boolean, false),
  suspectedTestProportion: new ValidationRuleModel(DataType.Boolean, false),

  reportCompleteness: new ValidationRuleModel(
    DataType.Number,
    false,
    `!(${Constants.Common.RootObjectNameSubstitute}.reportCompleteness >=0 && ${Constants.Common.RootObjectNameSubstitute}.reportCompleteness <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  reportCompletenessMetNotMet: new ValidationRuleModel(DataType.Boolean, false),
  reportTimeliness: new ValidationRuleModel(
    DataType.Number,
    false,
    `!(${Constants.Common.RootObjectNameSubstitute}.reportCompleteness >=0 && ${Constants.Common.RootObjectNameSubstitute}.reportCompleteness <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  variableCompleteness: new ValidationRuleModel(
    DataType.Number,
    false,
    `!(${Constants.Common.RootObjectNameSubstitute}.variableCompleteness >=0 && ${Constants.Common.RootObjectNameSubstitute}.variableCompleteness <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  variableConsistency: new ValidationRuleModel(
    DataType.Number,
    false,
    `!(${Constants.Common.RootObjectNameSubstitute}.variableConsistency >=0 && ${Constants.Common.RootObjectNameSubstitute}.variableConsistency <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  variableConcordance: new ValidationRuleModel(
    DataType.Number,
    false,
    `${Constants.Common.RootObjectNameSubstitute}.variableConcordance !==null && !(${Constants.Common.RootObjectNameSubstitute}.variableConcordance >=0 && ${Constants.Common.RootObjectNameSubstitute}.variableConcordance <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  variableConcordanceMetNotMet: new ValidationRuleModel(DataType.Boolean, false),
};

export default NationalLevelSummaryValidationRules;
