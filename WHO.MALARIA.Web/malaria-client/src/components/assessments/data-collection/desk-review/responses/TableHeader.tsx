import React from "react";

type TableHeaderProps = React.DetailedHTMLProps<
  React.HTMLAttributes<HTMLTableSectionElement>,
  HTMLTableSectionElement
> & {
  headers: Array<string>;
};
/** Renders table headers
 * @param headers an array of string
 */
function TableHeader(props: TableHeaderProps) {
  const { headers } = props;
  return (
    <thead {...props}>
      <tr>
        {headers.map((header: string, index: number) => (
          <th key={`${header}_${index}`}>{header}</th>
        ))}
      </tr>
    </thead>
  );
}

export const TableHead = (
  props: React.HTMLAttributes<HTMLTableSectionElement>
) => {
  const { children } = props;
  return <thead {...props}>{children}</thead>;
};

export default TableHeader;
