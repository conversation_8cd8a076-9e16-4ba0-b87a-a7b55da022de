using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Web.Middlewares
{
    /// <summary>
    /// Custom rate limiting middleware to prevent abuse and protect against DDoS attacks
    /// </summary>
    public class RateLimitingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<RateLimitingMiddleware> _logger;
        private readonly AppSettings _appSettings;
        private readonly ConcurrentDictionary<string, ClientRequestInfo> _clients;
        private readonly Timer _cleanupTimer;

        public RateLimitingMiddleware(RequestDelegate next, ILogger<RateLimitingMiddleware> logger, AppSettings appSettings)
        {
            _next = next;
            _logger = logger;
            _appSettings = appSettings;
            _clients = new ConcurrentDictionary<string, ClientRequestInfo>();

            // Cleanup expired entries every minute
            _cleanupTimer = new Timer(CleanupExpiredEntries, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Skip rate limiting if not enabled or configuration is missing
            if (_appSettings?.RateLimiting?.EnableGlobalRateLimit != true)
            {
                await _next(context);
                return;
            }

            var clientId = GetClientIdentifier(context);
            var endpoint = GetEndpointIdentifier(context);

            if (IsExemptFromRateLimit(context))
            {
                await _next(context);
                return;
            }

            // Get rate limit configuration for this endpoint or use global settings
            var (maxRequests, timeWindow) = GetRateLimitForEndpoint(endpoint);

            var clientInfo = _clients.GetOrAdd(clientId, _ => new ClientRequestInfo());
            var now = DateTime.UtcNow;

            lock (clientInfo)
            {
                // Clean up old requests outside the time window
                clientInfo.Requests.RemoveAll(r => now - r > timeWindow);

                // Check if client has exceeded rate limit
                if (clientInfo.Requests.Count >= maxRequests)
                {
                    _logger.LogWarning("Rate limit exceeded for client {ClientId} on endpoint {Endpoint}. Requests: {RequestCount}",
                        clientId, endpoint, clientInfo.Requests.Count);

                    await HandleRateLimitExceeded(context, clientInfo, maxRequests, timeWindow);
                    return;
                }

                // Add current request
                clientInfo.Requests.Add(now);
                clientInfo.LastRequestTime = now;
            }

            // Add rate limit headers
            AddRateLimitHeaders(context, clientInfo, maxRequests, timeWindow);

            await _next(context);
        }

        private string GetClientIdentifier(HttpContext context)
        {
            // Try to get real IP from forwarded headers (for load balancers/proxies)
            var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                var firstIp = forwardedFor.Split(',')[0].Trim();
                if (IPAddress.TryParse(firstIp, out _))
                    return firstIp;
            }

            var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp) && IPAddress.TryParse(realIp, out _))
                return realIp;

            // Fallback to connection remote IP
            return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        }

        private string GetEndpointIdentifier(HttpContext context)
        {
            return $"{context.Request.Method}:{context.Request.Path}";
        }

        private (int maxRequests, TimeSpan timeWindow) GetRateLimitForEndpoint(string endpoint)
        {
            // Check if per-endpoint rate limiting is enabled and endpoint has specific limits
            if (_appSettings.RateLimiting.EnablePerEndpointRateLimit &&
                _appSettings.RateLimiting.EndpointLimits?.ContainsKey(endpoint) == true)
            {
                var endpointLimit = _appSettings.RateLimiting.EndpointLimits[endpoint];
                return (endpointLimit.MaxRequests, TimeSpan.FromMinutes(endpointLimit.TimeWindowMinutes));
            }

            // Use global rate limit settings
            return (_appSettings.RateLimiting.MaxRequests, TimeSpan.FromMinutes(_appSettings.RateLimiting.TimeWindowMinutes));
        }

        private bool IsExemptFromRateLimit(HttpContext context)
        {
            // Exempt health check endpoints
            if (context.Request.Path.StartsWithSegments("/health") ||
                context.Request.Path.StartsWithSegments("/ping"))
                return true;

            // Exempt static files
            if (context.Request.Path.StartsWithSegments("/css") ||
                context.Request.Path.StartsWithSegments("/js") ||
                context.Request.Path.StartsWithSegments("/images") ||
                context.Request.Path.StartsWithSegments("/assets") ||
                context.Request.Path.StartsWithSegments("/favicon.ico"))
                return true;

            return false;
        }

        private async Task HandleRateLimitExceeded(HttpContext context, ClientRequestInfo clientInfo, int maxRequests, TimeSpan timeWindow)
        {
            context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
            context.Response.ContentType = "application/json";

            var retryAfter = timeWindow.TotalSeconds;
            context.Response.Headers.Add("Retry-After", retryAfter.ToString());

            AddRateLimitHeaders(context, clientInfo, maxRequests, timeWindow);

            var response = new
            {
                error = "Rate limit exceeded",
                message = $"Too many requests. Please try again in {retryAfter} seconds.",
                statusCode = 429,
                retryAfter = retryAfter
            };

            var jsonResponse = JsonSerializer.Serialize(response);
            await context.Response.WriteAsync(jsonResponse);
        }

        private void AddRateLimitHeaders(HttpContext context, ClientRequestInfo clientInfo, int maxRequests, TimeSpan timeWindow)
        {
            var remaining = Math.Max(0, maxRequests - clientInfo.Requests.Count);
            var resetTime = clientInfo.Requests.Count > 0
                ? clientInfo.Requests.Min().Add(timeWindow)
                : DateTime.UtcNow.Add(timeWindow);

            context.Response.Headers.Add("X-RateLimit-Limit", maxRequests.ToString());
            context.Response.Headers.Add("X-RateLimit-Remaining", remaining.ToString());
            context.Response.Headers.Add("X-RateLimit-Reset", ((DateTimeOffset)resetTime).ToUnixTimeSeconds().ToString());
        }

        private void CleanupExpiredEntries(object state)
        {
            var globalTimeWindow = TimeSpan.FromMinutes(_appSettings?.RateLimiting?.TimeWindowMinutes ?? 1);
            var cutoff = DateTime.UtcNow.Subtract(globalTimeWindow).Subtract(TimeSpan.FromMinutes(5));
            var keysToRemove = new List<string>();

            foreach (var kvp in _clients)
            {
                if (kvp.Value.LastRequestTime < cutoff)
                {
                    keysToRemove.Add(kvp.Key);
                }
            }

            foreach (var key in keysToRemove)
            {
                _clients.TryRemove(key, out _);
            }

            if (keysToRemove.Count > 0)
            {
                _logger.LogDebug("Cleaned up {Count} expired rate limit entries", keysToRemove.Count);
            }
        }

        public void Dispose()
        {
            _cleanupTimer?.Dispose();
        }
    }

    /// <summary>
    /// Tracks request information for a client
    /// </summary>
    public class ClientRequestInfo
    {
        public List<DateTime> Requests { get; set; } = new List<DateTime>();
        public DateTime LastRequestTime { get; set; } = DateTime.UtcNow;
    }



    /// <summary>
    /// Extension methods for rate limiting middleware
    /// </summary>
    public static class RateLimitingMiddlewareExtensions
    {
        public static IApplicationBuilder UseCustomRateLimiting(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<RateLimitingMiddleware>();
        }
    }
}
