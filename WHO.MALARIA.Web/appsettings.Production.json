{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning"}}, "AppSettings": {"ShowErrorDetails": false, "RateLimiting": {"MaxRequests": 60, "TimeWindowMinutes": 1, "EnableGlobalRateLimit": true, "EnablePerEndpointRateLimit": true, "EndpointLimits": {"POST:/api/auth/login": {"MaxRequests": 5, "TimeWindowMinutes": 15}, "POST:/api/auth/register": {"MaxRequests": 2, "TimeWindowMinutes": 60}, "POST:/api/auth/forgot-password": {"MaxRequests": 2, "TimeWindowMinutes": 60}, "POST:/api/assessment": {"MaxRequests": 10, "TimeWindowMinutes": 1}, "PUT:/api/assessment": {"MaxRequests": 10, "TimeWindowMinutes": 1}, "DELETE:/api/assessment": {"MaxRequests": 5, "TimeWindowMinutes": 1}, "POST:/api/dqa": {"MaxRequests": 20, "TimeWindowMinutes": 1}, "POST:/api/upload": {"MaxRequests": 10, "TimeWindowMinutes": 5}, "POST:/api/ratelimittest/endpoint-specific": {"MaxRequests": 2, "TimeWindowMinutes": 1}, "POST:/api/ratelimittest/heavy-operation": {"MaxRequests": 1, "TimeWindowMinutes": 1}}}}}