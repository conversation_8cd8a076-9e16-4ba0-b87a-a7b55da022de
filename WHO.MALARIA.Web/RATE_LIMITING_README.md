# Rate Limiting Implementation

This document describes the rate limiting security feature implemented to prevent abuse and protect against DDoS attacks.

## Overview

The application now includes comprehensive rate limiting middleware that:
- Prevents abuse by limiting the number of requests per client
- Protects against DDoS attacks
- Provides configurable limits per endpoint
- Returns proper HTTP 429 (Too Many Requests) responses
- Includes rate limit headers for client awareness

## Implementation Details

### Components Added

1. **RateLimitingMiddleware.cs** - Custom middleware for advanced rate limiting
2. **RateLimitingSetupExtension.cs** - Service registration extension
3. **AppSettings.cs** - Updated with rate limiting configuration
4. **appsettings.json** - Base rate limiting configuration
5. **appsettings.Development.json** - Lenient limits for development
6. **appsettings.Production.json** - Strict limits for production

### Configuration

Rate limiting is configured in `appsettings.json`:

```json
{
  "AppSettings": {
    "RateLimiting": {
      "MaxRequests": 100,
      "TimeWindowMinutes": 1,
      "EnableGlobalRateLimit": true,
      "EnablePerEndpointRateLimit": false,
      "EndpointLimits": {
        "POST:/api/auth/login": {
          "MaxRequests": 5,
          "TimeWindowMinutes": 15
        }
      }
    }
  }
}
```

### Environment-Specific Limits

- **Development**: 1000 requests/minute (lenient for testing)
- **Production**: 60 requests/minute (strict for security)

### Protected Endpoints

Special rate limits are applied to sensitive endpoints:
- Login: 5 attempts per 15 minutes
- Registration: 2-5 attempts per hour
- Password reset: 2-5 attempts per hour
- File uploads: 10 uploads per 5 minutes

### Exempted Paths

The following paths are exempt from rate limiting:
- Health check endpoints (`/health`, `/ping`)
- Static files (`/css`, `/js`, `/images`, `/assets`, `/favicon.ico`)

### Response Headers

When rate limiting is active, the following headers are included:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Unix timestamp when the limit resets
- `Retry-After`: Seconds to wait before retrying (when limit exceeded)

### Error Response

When rate limit is exceeded, a 429 status code is returned with:

```json
{
  "error": "Rate limit exceeded",
  "message": "Too many requests. Please try again in 60 seconds.",
  "statusCode": 429,
  "retryAfter": 60
}
```

## Client Identification

Clients are identified using:
1. `X-Forwarded-For` header (for load balancers)
2. `X-Real-IP` header (for proxies)
3. Connection remote IP address (fallback)

## Security Benefits

1. **DDoS Protection**: Prevents overwhelming the server with requests
2. **Brute Force Prevention**: Limits login and authentication attempts
3. **Resource Protection**: Prevents abuse of expensive operations
4. **Fair Usage**: Ensures resources are available to all users

## Monitoring

Rate limiting events are logged with:
- Client IP address
- Endpoint accessed
- Request count
- Timestamp

## Testing

To test rate limiting:

1. Make rapid requests to any API endpoint
2. Observe 429 responses after exceeding limits
3. Check response headers for rate limit information
4. Verify different limits for different endpoints

## Configuration Tips

- Adjust `MaxRequests` and `TimeWindowMinutes` based on your needs
- Use stricter limits in production
- Monitor logs to identify potential abuse patterns
- Consider implementing IP whitelisting for trusted sources

## Troubleshooting

If legitimate users are being rate limited:
1. Check if they're behind a shared IP (corporate network)
2. Consider increasing limits for specific endpoints
3. Implement user-based rate limiting instead of IP-based
4. Add IP whitelisting for trusted sources
