﻿using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands.DQA.Desk_Level;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.DeskLevel;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands.DQA.Desk_Level
{
    /// <summary>
    /// Handles Insertion of National Level Target and data quality reason command
    /// </summary>
    public class SaveNationalLevelTargetCommandHandler : RuleBase, IRequestHandler<SaveNationalLevelTargetCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public SaveNationalLevelTargetCommandHandler(
            IUnitOfWork unitOfWork,
            ITranslationService translationService,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assesmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assesmentRuleChecker;
        }

        /// <summary>
        /// Saves National Level Target and data quality reason in database
        /// </summary>
        /// <param name="request">Input Properties related National Level Result</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <returns>return true if saved successfully</returns>
        public async Task<bool> Handle(SaveNationalLevelTargetCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.SummaryData.AssessmentId, "AssessmentId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.SummaryData.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.SummaryData.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanEditAndSaveDeskLevelDQASummaryResult));

            Summary existingNationalResultSummary = await _unitOfWork.SummaryRepository.GetAsync(x => x.AssessmentId == request.SummaryData.AssessmentId && x.Year == request.SummaryData.Year && x.Type == (byte)DQADLSummaryResultType.NationalLevelTarget);
            if (existingNationalResultSummary == null)
            {

                Summary nationalResultSummary = new Summary
                {
                    Year = request.SummaryData.Year,
                    AssessmentId = request.SummaryData.AssessmentId,
                    Type = (byte)DQADLSummaryResultType.NationalLevelTarget,
                    IsFinalized = request.SummaryData.IsFinalized,
                    ReportCompleteness = request.SummaryData.ReportCompleteness,
                    ReportCompletenessMetNotMet = request.SummaryData.ReportCompletenessMetNotMet,
                    ReportTimeliness = request.SummaryData.ReportTimeliness,
                    VariableCompleteness = request.SummaryData.VariableCompleteness,
                    VariableConsistency = request.SummaryData.VariableConsistency,
                    VariableConcordance = request.SummaryData.VariableConcordance,
                    VariableConcordanceMetNotMet = request.SummaryData.VariableConcordanceMetNotMet,
                    MalariaOutpatientProportion = request.SummaryData.MalariaOutpatientProportion,
                    MalariaInPatientProportion = request.SummaryData.MalariaInPatientProportion,
                    MalariaInPatientDeathProportion = request.SummaryData.MalariaInPatientDeathProportion,
                    TestPositivityRate = request.SummaryData.TestPositivityRate,
                    SlidePositivityRate = request.SummaryData.SlidePositivityRate,
                    RDTPositivityRate = request.SummaryData.RDTPositivityRate,
                    SuspectedTestProportion = request.SummaryData.SuspectedTestProportion,
                };

                _unitOfWork.SummaryRepository.Add(nationalResultSummary);
            }
            else
            {
                existingNationalResultSummary.Year = request.SummaryData.Year;
                existingNationalResultSummary.AssessmentId = request.SummaryData.AssessmentId;
                existingNationalResultSummary.Type = (byte)DQADLSummaryResultType.NationalLevelTarget;
                existingNationalResultSummary.IsFinalized = request.SummaryData.IsFinalized;
                existingNationalResultSummary.ReportCompleteness = request.SummaryData.ReportCompleteness;
                existingNationalResultSummary.ReportCompletenessMetNotMet = request.SummaryData.ReportCompletenessMetNotMet;
                existingNationalResultSummary.ReportTimeliness = request.SummaryData.ReportTimeliness;
                existingNationalResultSummary.VariableCompleteness = request.SummaryData.VariableCompleteness;
                existingNationalResultSummary.VariableConsistency = request.SummaryData.VariableConsistency;
                existingNationalResultSummary.VariableConcordance = request.SummaryData.VariableConcordance;
                existingNationalResultSummary.VariableConcordanceMetNotMet = request.SummaryData.VariableConcordanceMetNotMet;
                existingNationalResultSummary.MalariaOutpatientProportion = request.SummaryData.MalariaOutpatientProportion;
                existingNationalResultSummary.MalariaInPatientProportion = request.SummaryData.MalariaInPatientProportion;
                existingNationalResultSummary.MalariaInPatientDeathProportion = request.SummaryData.MalariaInPatientDeathProportion;
                existingNationalResultSummary.TestPositivityRate = request.SummaryData.TestPositivityRate;
                existingNationalResultSummary.SlidePositivityRate = request.SummaryData.SlidePositivityRate;
                existingNationalResultSummary.RDTPositivityRate = request.SummaryData.RDTPositivityRate;
                existingNationalResultSummary.SuspectedTestProportion = request.SummaryData.SuspectedTestProportion;

                _unitOfWork.SummaryRepository.Update(existingNationalResultSummary);
            }

            SummaryDataQualityResultReason existingResultReason = await _unitOfWork.SummaryDataQualityResultReasonRepository.GetAsync(x => x.AssessmentId == request.SummaryData.AssessmentId && x.Year == request.SummaryData.Year);

            if (existingResultReason == null)
            {
                SummaryDataQualityResultReason resultReason = new SummaryDataQualityResultReason
                {
                    AssessmentId = request.SummaryData.AssessmentId,
                    Year = request.SummaryData.Year,
                    IsFinalized = request.SummaryData.IsFinalized,
                    Reason = request.SummaryData.DataQualityResultReason,
                };

                _unitOfWork.SummaryDataQualityResultReasonRepository.Add(resultReason);
            }
            else
            {
                existingResultReason.AssessmentId = request.SummaryData.AssessmentId;
                existingResultReason.Year = request.SummaryData.Year;
                existingResultReason.IsFinalized = request.SummaryData.IsFinalized;
                existingResultReason.Reason = request.SummaryData.DataQualityResultReason;

                _unitOfWork.SummaryDataQualityResultReasonRepository.Update(existingResultReason);
            }

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }
    }
}
