﻿using System;

namespace WHO.MALARIA.Domain.Models.DQA.DeskLevel
{
    /// <summary>
    /// Contains DQA desk level summary results
    /// </summary>
    public class Summary : ModelBase
    {
        public Guid Id { get; set; }
        public Guid AssessmentId { get; set; }
        public Assessment Assessment { get; set; }
        public double? ReportCompleteness { get; set; }
        public bool? ReportCompletenessMetNotMet { get; set; }
        public double? ReportTimeliness { get; set; }
        public double? VariableCompleteness { get; set; }
        public double? VariableConsistency { get; set; }
        public double? VariableConcordance { get; set; }
        public bool? VariableConcordanceMetNotMet { get; set; }
        public bool? MalariaOutpatientProportion { get; set; }
        public bool? MalariaInPatientProportion { get; set; }
        public bool? MalariaInPatientDeathProportion { get; set; }
        public bool? TestPositivityRate { get; set; }
        public bool? SlidePositivityRate { get; set; }
        public bool? RDTPositivityRate { get; set; }
        public bool? SuspectedTestProportion { get; set; }
        public double? KeyVariableCompletenessWithinRegister { get; set; }
        public double? KeyVariableConcordanceBtwRegister { get; set; }
        public double? DataSoucesError { get; set; }
        public byte Type { get; set; } //1 For NationalLevelResult 2 for NationalLevelTarget
        public short Year { get; set; }
        public bool IsFinalized { get; set; }
    }
}
