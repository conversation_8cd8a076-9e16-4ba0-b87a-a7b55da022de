﻿using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos;

namespace WHO.MALARIA.Features.Helpers
{
    /// <summary>
    /// Provides extension methods which in turn does the common tasks
    /// </summary>
    public static class UtilityHelper
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="file"></param>
        /// <returns>Byte array from the file</returns>
        public static async Task<byte[]> GetBytesAsync(this IFormFile file)
        {
            using (var memoryStream = new MemoryStream())
            {
                await file.CopyToAsync(memoryStream);
                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// Get name of the caller method
        /// </summary>
        /// <param name="methodName">Optional! - Name of the caller method</param>
        /// <returns> Name of method from which this method is invoked</returns>
        public static string GetCallerMethodName([CallerMemberName] string methodName = null) => methodName;

        /// <summary>
        /// Gets name column for given culture,
        /// </summary>
        /// <param name="culture">Culture for which columns need to be fetched</param>
        /// <returns>Column name of the 'Name' column in given culture</returns>
        public static string GetNameColumn(this string culture)
        {
            if (culture?.ToLower() == "en" || string.IsNullOrWhiteSpace(culture))
            {
                return "Name";
            }
            else
            {
                return string.Concat("Name_", culture.ToUpper());
            }
        }

        /// <summary>
        /// Gets description column for given culture,
        /// </summary>
        /// <param name="culture">Culture for which columns need to be fetched</param>
        /// <returns>Column name of the 'Description' column in given culture</returns>
        public static string GetDescriptionColumn(this string culture)
        {
            if (culture?.ToLower() == "en" || string.IsNullOrWhiteSpace(culture))
            {
                return "Description";
            }
            else
            {
                return string.Concat("Description_", culture.ToUpper());
            }
        }

        /// <summary>
        /// Gets Short description column for given culture,
        /// </summary>
        /// <param name="culture">Culture for which columns need to be fetched</param>
        /// <returns>Column name of the 'ShortDescription' column in given culture</returns>
        public static string GetShortDescriptionColumn(this string culture)
        {
            if (culture?.ToLower() == "en" || string.IsNullOrWhiteSpace(culture))
            {
                return "ShortDescription";
            }
            else
            {
                return string.Concat("ShortDescription_", culture.ToUpper());
            }
        }

        /// <summary>
        /// Gets question column for given culture,
        /// </summary>
        /// <param name="culture">Culture for which columns need to be fetched</param>
        /// <returns>Column name of the 'Question' column in given culture</returns>
        public static string GetQuestionColumn(this string culture)
        {
            if (culture?.ToLower() == "en" || string.IsNullOrWhiteSpace(culture))
            {
                return "Question";
            }
            else
            {
                return string.Concat("Question_", culture.ToUpper());
            }
        }

        /// <summary>
        /// Gets notes column for given culture,
        /// </summary>
        /// <param name="culture">Culture for which columns need to be fetched</param>
        /// <returns>Column name of the 'Notes' column in given culture</returns>
        public static string GetNotesColumn(this string culture)
        {
            if (culture?.ToLower() == "en" || string.IsNullOrWhiteSpace(culture))
            {
                return "Notes";
            }
            else
            {
                return string.Concat("Notes_", culture.ToUpper());
            }
        }

        /// <summary>
        /// Gets options column for given culture,
        /// </summary>
        /// <param name="culture">Culture for which columns need to be fetched</param>
        /// <returns>Column name of the 'Options' column in given culture</returns>
        public static string GetOptionsColumn(this string culture)
        {
            if (culture?.ToLower() == "en" || string.IsNullOrWhiteSpace(culture))
            {
                return "Options";
            }
            else
            {
                return string.Concat("Options_", culture.ToUpper());
            }
        }

        /// <summary>
        /// Create a ZIP file for images
        /// </summary>
        /// <param name="files">Files to be added in a ZIP file</param>
        /// <returns>Byte array of the created ZIP file.</returns>
        public static byte[] CreateZIPFileForImages(IEnumerable<FileDto> files)
        {
            byte[] memoryStream = null;

            using (MemoryStream ms = new MemoryStream())
            {
                using (ZipArchive archive = new ZipArchive(ms, ZipArchiveMode.Create, true))
                {
                    foreach (var file in files)
                    {
                        ZipArchiveEntry zipArchiveEntry = archive.CreateEntry(file.FileName + file.Extension, CompressionLevel.NoCompression);

                        byte[] imagedata = Convert.FromBase64String(file.File);

                        using (Stream zipstream2 = zipArchiveEntry.Open())
                        {
                            zipstream2.Write(imagedata, 0, imagedata.Length);
                        }
                    }
                }
                memoryStream = ms.ToArray();
            }

            return memoryStream;
        }

        /// <summary>
        /// Create a ZIP file
        /// </summary>
        /// <param name="files">Files to be added in a ZIP file</param>
        /// <returns>Byte array of the created ZIP file.</returns>
        public static byte[] CreateZIPFile(IEnumerable<FileResponseDto> files)
        {
            byte[] memoryStream = null;

            using (MemoryStream ms = new MemoryStream())
            {
                using (ZipArchive archive = new ZipArchive(ms, ZipArchiveMode.Create, true))
                {
                    foreach (FileResponseDto file in files)
                    {
                        ZipArchiveEntry zipArchiveEntry = archive.CreateEntry(file.FileName, CompressionLevel.NoCompression);
                        using (Stream stream = zipArchiveEntry.Open())
                        {
                            stream.Write(file.FileData, 0, file.FileData.Length);
                        }
                    }
                }

                memoryStream = ms.ToArray();
            }

            return memoryStream;
        }

        public static string GetApplicationURL(this HttpContext httpContext) => $"{httpContext.Request.Scheme}://{httpContext.Request.Host.ToUriComponent()}";

        /// <summary>
        /// Method to get Enumerable data as dictionary object
        /// </summary>
        /// <param name="enumType">parameter as enum type</param>
        /// <returns>dictionary object containing enum values as key-value pair</returns>
        public static Dictionary<string, int> GetEnumerableData<TEnum>()
        {
            var resultSet = new Dictionary<string, int>();
            resultSet = Enum.GetValues(typeof(TEnum)).Cast<TEnum>().ToDictionary(x => x.ToString(), v => Convert.ToInt32(v));
            return resultSet;
        }

        /// <summary>
        /// To convert string into integer if not empty
        /// </summary>
        /// <param name="input">string value</param>
        /// <returns>integer value</returns>
        public static int? ConvertStringToInt(string input)
        {

            if (!string.IsNullOrEmpty(input))
            {
                return Convert.ToInt32(input);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// To convert string into byte if not empty
        /// </summary>
        /// <param name="input">string value</param>
        /// <returns>boolean value</returns>
        public static bool ConvertStringToBoolean(string input)
        {
            if (input == null)
                return false;

            if (input == "0")
                return false;

            if (input == "1")
                return true;

            return false;
        }

        /// <summary>
        /// Method to get data from model and map it into entity based on property
        /// </summary>
        /// <typeparam name="T">Type for Entity</typeparam>
        /// <typeparam name="T1">Type for model</typeparam>
        /// <param name="instanceData">List of instance of type</param>
        /// <param name="assessmentId">guid</param>
        /// <returns>List of instance of Entity</returns>
        public static List<T> GetEntityDataFromModel<T, T1>(List<T1> instanceData, Guid assessmentId, Guid createdBy)
        {
            var entityData = new List<T>();

            Type typeOfEntityObject = typeof(T);
            Type typeOfRequestObject = typeof(T1);

            PropertyInfo[] entityObjectProperties = typeOfEntityObject.GetProperties();
            PropertyInfo[] requestObjectProperties = typeOfRequestObject.GetProperties();

            instanceData.ForEach((T1 instance) =>
            {
                T instanceObject = (T)Activator.CreateInstance(typeOfEntityObject);

                foreach (PropertyInfo requestProperty in requestObjectProperties)
                {
                    foreach (PropertyInfo entityProperty in entityObjectProperties)
                    {
                        if (entityProperty.Name == requestProperty.Name)
                        {
                            PropertyInfo propertyInfo = instance.GetType().GetProperty(entityProperty.Name);
                            object propertyValue = propertyInfo.GetValue(instance, null);
                            Type propertyType = propertyInfo.PropertyType;

                            if (Nullable.GetUnderlyingType(propertyType) != null)
                            {
                                // It's a nullable type
                                Type newPropertyType = Nullable.GetUnderlyingType(propertyType);
                                if (propertyValue != null)
                                {
                                    entityProperty.SetValue(instanceObject, Convert.ChangeType(propertyValue, newPropertyType));
                                }
                                else
                                {
                                    entityProperty.SetValue(instanceObject, null);
                                }
                            }
                            else
                            {
                                // It's not a nullable type
                                entityProperty.SetValue(instanceObject, Convert.ChangeType(propertyValue, propertyType));
                            }
                        }
                        else
                        {
                            if (entityProperty.Name == "AssessmentId")
                            {
                                entityProperty.SetValue(instanceObject, assessmentId);
                            }

                            if (entityProperty.Name == "CreatedBy")
                            {
                                entityProperty.SetValue(instanceObject, createdBy);
                            }
                        }
                    }
                }

                entityData.Add(instanceObject);

            });

            return entityData;
        }

        /// <summary>
        /// Method to get data into model from entity based on property
        /// </summary>
        /// <typeparam name="T">Type for model</typeparam>
        /// <typeparam name="T1">Type for Entity</typeparam>
        /// <param name="instanceData">List of instance of type</param>
        /// <param name="assessmentId">guid</param>
        /// <returns>List of instance of model</returns>
        public static List<T> GetDataInModelFromEntity<T, T1>(List<T1> instanceData)
        {
            var modelData = new List<T>();

            Type typeOfRequestObject = typeof(T);
            Type typeOfEntityObject = typeof(T1);

            PropertyInfo[] entityObjectProperties = typeOfEntityObject.GetProperties();
            PropertyInfo[] requestObjectProperties = typeOfRequestObject.GetProperties();

            instanceData.ForEach((T1 instance) =>
            {
                T instanceObject = (T)Activator.CreateInstance(typeOfRequestObject);

                foreach (PropertyInfo entityProperty in entityObjectProperties)
                {
                    foreach (PropertyInfo requestProperty in requestObjectProperties)
                    {
                        if (entityProperty.Name == requestProperty.Name)
                        {
                            PropertyInfo propertyInfo = instance.GetType().GetProperty(requestProperty.Name);
                            object propertyValue = propertyInfo.GetValue(instance, null);
                            Type propertyType = propertyInfo.PropertyType;
                            if (Nullable.GetUnderlyingType(propertyType) != null)
                            {
                                // It's a nullable type
                                Type newPropertyType = Nullable.GetUnderlyingType(propertyType);

                                if (propertyValue != null)
                                {
                                    requestProperty.SetValue(instanceObject, Convert.ChangeType(propertyValue, newPropertyType));
                                }
                                else
                                {
                                    requestProperty.SetValue(instanceObject, null);
                                }
                            }
                            else
                            {
                                // It's not a nullable type
                                requestProperty.SetValue(instanceObject, Convert.ChangeType(propertyValue, propertyType));
                            }
                        }
                    }
                }

                modelData.Add(instanceObject);

            });

            return modelData;
        }

        public static object GetPropValue(object src, string propName)
        {
            if (src.GetType().GetProperty(propName) != null)
            {
                return src.GetType().GetProperty(propName).GetValue(src, null);
            }
            return null;
        }

        /// <summary>
        /// Converts List into DataTable
        /// </summary>
        /// <typeparam name="T">Type of List</typeparam>
        /// <param name="data">Data of List Type</param>
        /// <returns>Instance of DataTable</returns>
        public static DataTable ToDataTable<T>(this IList<T> data)
        {
            PropertyDescriptorCollection properties =
                TypeDescriptor.GetProperties(typeof(T));
            DataTable table = new DataTable();
            foreach (PropertyDescriptor prop in properties)
                table.Columns.Add(prop.Name, Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType);
            foreach (T item in data)
            {
                DataRow row = table.NewRow();
                foreach (PropertyDescriptor prop in properties)
                    row[prop.Name] = prop.GetValue(item) ?? DBNull.Value;
                table.Rows.Add(row);
            }
            return table;
        }

        /// <summary>
        /// To transform table into different format
        /// </summary>
        /// <param name="source">Type of source</param>
        /// <param name="columnSelector">Type of column selector</param>
        /// <param name="rowSelector">Expression row selector</param>
        /// <param name="dataSelector">Type for data selector</param>
        /// <returns>Instance of DataTable</returns>
        public static DataTable ToPivotTable<T, TColumn, TRow, TData>(
                                                this IEnumerable<T> source,
                                                Func<T, TColumn> columnSelector,
                                                Expression<Func<T, TRow>> rowSelector,
                                                Func<IEnumerable<T>, TData> dataSelector)
        {
            DataTable table = new DataTable();
            string rowName = ((MemberExpression)rowSelector.Body).Member.Name;
            table.Columns.Add(new DataColumn(rowName));
            IEnumerable<TColumn> columns = source.Select(columnSelector).Distinct();

            foreach (TColumn column in columns)
            {
                DataColumn dataColumn = new DataColumn()
                {
                    ColumnName = column.ToString(),
                    DataType = typeof(object)
                };

                table.Columns.Add(dataColumn);
            }

            var rows = source.GroupBy(rowSelector.Compile())
                       .Select(rowGroup => new
                       {
                           Key = rowGroup.Key,
                           Values = columns.GroupJoin(
                               rowGroup,
                               c => c,
                               r => columnSelector(r),
                               (c, columnGroup) => dataSelector(columnGroup))
                       });

            foreach (var row in rows)
            {
                DataRow dataRow = table.NewRow();
                List<object> items = row.Values.Cast<object>().ToList();
                items.Insert(0, row.Key);
                List<object> newitems = new List<object>();

                for (int i = 0; i < items.Count; i++)
                {
                    if (double.TryParse(Convert.ToString(items[i]), out double number))
                        newitems.Add(Math.Round(number, 2));
                    else
                        newitems.Add(items[i]);
                }

                dataRow.ItemArray = newitems.ToArray();

                table.Rows.Add(dataRow);
            }

            return table;
        }

        /// <summary>
        /// Returns the description of the enum attribute property requested
        /// </summary>      
        /// <param name="value">Enum value</param>
        /// <param name="language">language code </param>
        /// <returns>Description</returns>
        public static string GetTranslationKey(this Enum value)
        {
            Type type = value.GetType();

            FieldInfo fieldInfo = type.GetField(value.ToString());

            TranslationKeyAttribute enumAttribute = (TranslationKeyAttribute)fieldInfo.GetCustomAttributes(typeof(TranslationKeyAttribute), false).FirstOrDefault();

            return enumAttribute.TranslationKey;
        }

        /// <summary>
        /// Returns the Item property description of the enum attribute property
        /// </summary>
        /// <param name="type">Enum type</param>
        /// <param name="language">language code</param>
        /// <returns>Instance of Dictionary<string, string></returns>
        public static Dictionary<string, string> GetEnumItemsTranslationKey(Type type)
        {
            var itemWithDescription = new Dictionary<string, string>();
            string[] names = Enum.GetNames(type);
            foreach (var name in names)
            {
                FieldInfo fieldInfo = type.GetField(name);
                object[] fields = fieldInfo.GetCustomAttributes(typeof(TranslationKeyAttribute), true);
                foreach (TranslationKeyAttribute field in fields)
                {
                    itemWithDescription.Add(name, field.TranslationKey);
                }
            }
            return itemWithDescription;
        }

        /// <summary>
        ///  Calculate percentage of two numbers
        /// </summary>
        /// <param name="numerator">Numerator</param>
        /// <param name="denominator">Denominator</param>
        /// <returns>percentage</returns>
        public static decimal CalculatePercentage(this int numerator, int denominator)
        {
            return denominator > 0 ? ((decimal)numerator / denominator * 100) : 0;
        }

        /// <summary>
        /// Returns same string with first letter as lowercase
        /// </summary>
        /// <param name="input">Input string</param>
        /// <returns>Instance of string</returns>
        public static string ToLowerFirstChar(this string input, int charLength)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            string firstPart = input.Substring(0, charLength).ToLower();
            string backPart = input.Substring(charLength);

            return firstPart + backPart;
        }

        /// <summary>
        /// Get Yes / No based on boolean input
        /// </summary>
        /// <param name="input"></param>
        /// <returns>Instance of string</returns>
        public static string ToYesNo(this bool input)
        {
            if (input)
                return "Yes";
            else
                return "No";
        }

        /// <summary>
        /// Get Met / Not Met based on boolean input
        /// </summary>
        /// <param name="input"></param>
        /// <returns>Instance of string</returns>
        public static string ToMetNotmet(this bool input)
        {
            if (input)
                return "Met";
            else
                return "Not met";
        }

        /// <summary>
        /// Get Met / Not Met based on boolean input
        /// </summary>
        /// <param name="input"></param>
        /// <returns>Instance of string</returns>
        public static string ToYesNoNA(this bool? input)
        {
            return input.HasValue ? input.Value.ToYesNo() : "NA";
        }

        /// <summary>
        /// Get Met / Not Met based on nullable boolean input
        /// </summary>
        /// <param name="input"></param>
        /// <returns>Instance of string</returns>
        public static string ToMetNotmetOrEmpty(this bool? input)
        {
            return input.HasValue ? input.Value.ToMetNotmet() : "";
        }

        /// <summary>
        /// Get desk level burden reduction strategy met not met status
        /// </summary>
        /// <param name="percentage">Percentage</param>
        /// <returns>Met not met status</returns>
        public static int GetDeskLevelBurdenReductionStrategyMetNotMetStatus(this float percentage)
        {
            if (percentage > 95 && percentage <= 100)
            {
                return (int)Domain.Enum.MetNotMetStatus.Met;
            }
            else if (percentage <= 95 && percentage >= 80)
            {
                return (int)Domain.Enum.MetNotMetStatus.PartiallyMet;
            }
            else if ((percentage < 80 && percentage >= 0) || percentage > 100)
            {
                return (int)Domain.Enum.MetNotMetStatus.NotMet;
            }
            else
            {
                return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
            }
        }

        /// <summary>
        /// Get desk level elimination met not met status
        /// </summary>
        /// <param name="percentage">Percentage</param>
        /// <returns>Met not met status</returns>
        public static int GetDeskLevelEliminationStrategyMetNotMetStatus(this float percentage)
        {
            if (percentage > 95 && percentage <= 100)
            {
                return (int)Domain.Enum.MetNotMetStatus.Met;
            }
            else if (percentage <= 95 && percentage >= 80)
            {
                return (int)Domain.Enum.MetNotMetStatus.PartiallyMet;
            }
            else if ((percentage < 80 && percentage >= 0) || percentage > 100)
            {
                return (int)Domain.Enum.MetNotMetStatus.NotMet;
            }
            else
            {
                return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
            }
        }

        /// <summary>
        /// Remove text from string
        /// </summary>
        /// <param name="name">Input string</param>
        /// <param name="textToRemove">Text to remove from string</param>
        /// <returns>String</returns>
        public static string RemoveText(this string name, string textToRemove)
        {
            int position = name.IndexOf(textToRemove);

            if (position != -1)
            {
                name = name.Remove(position).Trim();
            }

            return name;
        }

        /// <summary>
        /// Method to replace null value if it exist in input obj 
        /// </summary>
        /// <param name="obj">obj</param>
        /// <returns>string</returns>
        public static string ReplaceToNullIfExist(this object obj)
        {
            return !string.IsNullOrWhiteSpace(obj.ToString()) ? obj.ToString() : null;
        }


    }
}
